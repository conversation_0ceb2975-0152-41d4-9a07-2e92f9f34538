{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nclass DashboardConfigService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = environment.engineUrl;\n    this.configSubject = new BehaviorSubject(null);\n    this.config$ = this.configSubject.asObservable();\n  }\n  /**\n   * Load dashboard configuration from backend\n   */\n  loadConfig() {\n    return this.http.get(`${this.baseUrl}api/smart-dashboard/config`);\n  }\n  /**\n   * Set configuration and notify subscribers\n   */\n  setConfig(config) {\n    this.configSubject.next(config);\n  }\n  /**\n   * Get current configuration\n   */\n  getCurrentConfig() {\n    return this.configSubject.value;\n  }\n  /**\n   * Get chart colors from config\n   */\n  getChartColors() {\n    const config = this.getCurrentConfig();\n    return config?.chart_colors || [];\n  }\n  /**\n   * Get currency symbol from config\n   */\n  getCurrencySymbol() {\n    const config = this.getCurrentConfig();\n    return config?.currency?.symbol || '₹';\n  }\n  /**\n   * Get summary card color based on data type\n   */\n  getSummaryCardColor(dataType) {\n    const config = this.getCurrentConfig();\n    return config?.summary_card_config?.colors?.[dataType] || '#ffb366';\n  }\n  /**\n   * Get summary card icon based on data type\n   */\n  getSummaryCardIcon(dataType) {\n    const config = this.getCurrentConfig();\n    return config?.summary_card_config?.icons?.[dataType] || 'analytics';\n  }\n  /**\n   * Get default chart options from config\n   */\n  getDefaultChartOptions() {\n    const config = this.getCurrentConfig();\n    return config?.default_chart_options || {\n      responsive: true,\n      maintainAspectRatio: false\n    };\n  }\n  /**\n   * Get available dashboard types\n   */\n  getDashboardTypes() {\n    const config = this.getCurrentConfig();\n    return config?.dashboard_types || [];\n  }\n  /**\n   * Get available base date options\n   */\n  getBaseDateOptions() {\n    const config = this.getCurrentConfig();\n    return config?.base_date_options || [];\n  }\n  /**\n   * Get UI configuration defaults\n   */\n  getUIConfig() {\n    const config = this.getCurrentConfig();\n    return config?.ui_config || {\n      default_date_range_days: 30,\n      default_dashboard_type: 'purchase',\n      default_base_date: 'deliveryDate'\n    };\n  }\n  /**\n   * Get chart type display name\n   */\n  getChartTypeDisplayName(type) {\n    const config = this.getCurrentConfig();\n    return config?.chart_types?.[type] || type;\n  }\n  /**\n   * Merge chart options with defaults\n   */\n  mergeChartOptions(chartOptions) {\n    const defaultOptions = this.getDefaultChartOptions();\n    return this.deepMerge(defaultOptions, chartOptions || {});\n  }\n  /**\n   * Deep merge utility function\n   */\n  deepMerge(target, source) {\n    const result = {\n      ...target\n    };\n    for (const key in source) {\n      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {\n        result[key] = this.deepMerge(result[key] || {}, source[key]);\n      } else {\n        result[key] = source[key];\n      }\n    }\n    return result;\n  }\n  /**\n   * Format currency value\n   */\n  formatCurrency(value) {\n    const symbol = this.getCurrencySymbol();\n    return `${symbol}${value.toLocaleString()}`;\n  }\n  /**\n   * Format large numbers with K/L suffixes\n   */\n  formatLargeNumber(value) {\n    const symbol = this.getCurrencySymbol();\n    if (value >= 10000000) {\n      // 1 Crore\n      return `${symbol}${(value / 10000000).toFixed(1)}Cr`;\n    } else if (value >= 100000) {\n      // 1 Lakh\n      return `${symbol}${(value / 100000).toFixed(1)}L`;\n    } else if (value >= 1000) {\n      // 1 Thousand\n      return `${symbol}${(value / 1000).toFixed(0)}K`;\n    } else {\n      return `${symbol}${value.toLocaleString()}`;\n    }\n  }\n  /**\n   * Get semantic colors for different data types\n   */\n  getSemanticColors() {\n    return {\n      positive: '#28a745',\n      negative: '#dc3545',\n      neutral: '#6c757d',\n      warning: '#ffc107',\n      info: '#17a2b8',\n      primary: '#007bff' // Blue\n    };\n  }\n  /**\n   * Get color based on value type and context\n   */\n  getContextualColor(value, context = 'default') {\n    const semanticColors = this.getSemanticColors();\n    switch (context) {\n      case 'growth':\n      case 'profit':\n        return value >= 0 ? semanticColors['positive'] : semanticColors['negative'];\n      case 'loss':\n      case 'spoilage':\n        return semanticColors['negative'];\n      case 'movement':\n      case 'transfer':\n        return semanticColors['info'];\n      case 'warning':\n        return semanticColors['warning'];\n      default:\n        return semanticColors['primary'];\n    }\n  }\n  static {\n    this.ɵfac = function DashboardConfigService_Factory(t) {\n      return new (t || DashboardConfigService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: DashboardConfigService,\n      factory: DashboardConfigService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\nexport { DashboardConfigService };", "map": {"version": 3, "names": ["BehaviorSubject", "environment", "DashboardConfigService", "constructor", "http", "baseUrl", "engineUrl", "configSubject", "config$", "asObservable", "loadConfig", "get", "setConfig", "config", "next", "getCurrentConfig", "value", "getChartColors", "chart_colors", "getCurrencySymbol", "currency", "symbol", "getSummaryCardColor", "dataType", "summary_card_config", "colors", "getSummaryCardIcon", "icons", "getDefaultChartOptions", "default_chart_options", "responsive", "maintainAspectRatio", "getDashboardTypes", "dashboard_types", "getBaseDateOptions", "base_date_options", "getUIConfig", "ui_config", "default_date_range_days", "default_dashboard_type", "default_base_date", "getChartTypeDisplayName", "type", "chart_types", "mergeChartOptions", "chartOptions", "defaultOptions", "deepMerge", "target", "source", "result", "key", "Array", "isArray", "formatCurrency", "toLocaleString", "formatLargeNumber", "toFixed", "getSemanticColors", "positive", "negative", "neutral", "warning", "info", "primary", "getContextualColor", "context", "semanticColors", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\digii\\digitorywebv4\\src\\app\\services\\dashboard-config.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, BehaviorSubject } from 'rxjs';\nimport { environment } from '../../environments/environment';\n\nexport interface DashboardType {\n  value: string;\n  label: string;\n}\n\nexport interface BaseDateOption {\n  value: string;\n  label: string;\n}\n\nexport interface DashboardConfig {\n  chart_colors: string[];\n  chart_types: { [key: string]: string };\n  currency: {\n    code: string;\n    symbol: string;\n  };\n  dashboard_types: DashboardType[];\n  base_date_options: BaseDateOption[];\n  default_chart_options: any;\n  summary_card_config: {\n    colors: { [key: string]: string };\n    icons: { [key: string]: string };\n  };\n  ui_config: {\n    default_date_range_days: number;\n    default_dashboard_type: string;\n    default_base_date: string;\n  };\n}\n\nexport interface DashboardConfigResponse {\n  status: string;\n  data: DashboardConfig;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class DashboardConfigService {\n  private baseUrl: string = environment.engineUrl;\n  private configSubject = new BehaviorSubject<DashboardConfig | null>(null);\n  public config$ = this.configSubject.asObservable();\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * Load dashboard configuration from backend\n   */\n  loadConfig(): Observable<DashboardConfigResponse> {\n    return this.http.get<DashboardConfigResponse>(`${this.baseUrl}api/smart-dashboard/config`);\n  }\n\n  /**\n   * Set configuration and notify subscribers\n   */\n  setConfig(config: DashboardConfig): void {\n    this.configSubject.next(config);\n  }\n\n  /**\n   * Get current configuration\n   */\n  getCurrentConfig(): DashboardConfig | null {\n    return this.configSubject.value;\n  }\n\n  /**\n   * Get chart colors from config\n   */\n  getChartColors(): string[] {\n    const config = this.getCurrentConfig();\n    return config?.chart_colors || [];\n  }\n\n  /**\n   * Get currency symbol from config\n   */\n  getCurrencySymbol(): string {\n    const config = this.getCurrentConfig();\n    return config?.currency?.symbol || '₹';\n  }\n\n  /**\n   * Get summary card color based on data type\n   */\n  getSummaryCardColor(dataType: string): string {\n    const config = this.getCurrentConfig();\n    return config?.summary_card_config?.colors?.[dataType] || '#ffb366';\n  }\n\n  /**\n   * Get summary card icon based on data type\n   */\n  getSummaryCardIcon(dataType: string): string {\n    const config = this.getCurrentConfig();\n    return config?.summary_card_config?.icons?.[dataType] || 'analytics';\n  }\n\n  /**\n   * Get default chart options from config\n   */\n  getDefaultChartOptions(): any {\n    const config = this.getCurrentConfig();\n    return config?.default_chart_options || {\n      responsive: true,\n      maintainAspectRatio: false\n    };\n  }\n\n  /**\n   * Get available dashboard types\n   */\n  getDashboardTypes(): DashboardType[] {\n    const config = this.getCurrentConfig();\n    return config?.dashboard_types || [];\n  }\n\n  /**\n   * Get available base date options\n   */\n  getBaseDateOptions(): BaseDateOption[] {\n    const config = this.getCurrentConfig();\n    return config?.base_date_options || [];\n  }\n\n  /**\n   * Get UI configuration defaults\n   */\n  getUIConfig(): any {\n    const config = this.getCurrentConfig();\n    return config?.ui_config || {\n      default_date_range_days: 30,\n      default_dashboard_type: 'purchase',\n      default_base_date: 'deliveryDate'\n    };\n  }\n\n  /**\n   * Get chart type display name\n   */\n  getChartTypeDisplayName(type: string): string {\n    const config = this.getCurrentConfig();\n    return config?.chart_types?.[type] || type;\n  }\n\n  /**\n   * Merge chart options with defaults\n   */\n  mergeChartOptions(chartOptions: any): any {\n    const defaultOptions = this.getDefaultChartOptions();\n    return this.deepMerge(defaultOptions, chartOptions || {});\n  }\n\n  /**\n   * Deep merge utility function\n   */\n  private deepMerge(target: any, source: any): any {\n    const result = { ...target };\n    \n    for (const key in source) {\n      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {\n        result[key] = this.deepMerge(result[key] || {}, source[key]);\n      } else {\n        result[key] = source[key];\n      }\n    }\n    \n    return result;\n  }\n\n  /**\n   * Format currency value\n   */\n  formatCurrency(value: number): string {\n    const symbol = this.getCurrencySymbol();\n    return `${symbol}${value.toLocaleString()}`;\n  }\n\n  /**\n   * Format large numbers with K/L suffixes\n   */\n  formatLargeNumber(value: number): string {\n    const symbol = this.getCurrencySymbol();\n    \n    if (value >= 10000000) { // 1 Crore\n      return `${symbol}${(value / 10000000).toFixed(1)}Cr`;\n    } else if (value >= 100000) { // 1 Lakh\n      return `${symbol}${(value / 100000).toFixed(1)}L`;\n    } else if (value >= 1000) { // 1 Thousand\n      return `${symbol}${(value / 1000).toFixed(0)}K`;\n    } else {\n      return `${symbol}${value.toLocaleString()}`;\n    }\n  }\n\n  /**\n   * Get semantic colors for different data types\n   */\n  getSemanticColors(): { [key: string]: string } {\n    return {\n      positive: '#28a745',    // Green\n      negative: '#dc3545',    // Red\n      neutral: '#6c757d',     // Gray\n      warning: '#ffc107',     // Yellow\n      info: '#17a2b8',        // Cyan\n      primary: '#007bff'      // Blue\n    };\n  }\n\n  /**\n   * Get color based on value type and context\n   */\n  getContextualColor(value: number, context: string = 'default'): string {\n    const semanticColors = this.getSemanticColors();\n    \n    switch (context) {\n      case 'growth':\n      case 'profit':\n        return value >= 0 ? semanticColors['positive'] : semanticColors['negative'];\n      case 'loss':\n      case 'spoilage':\n        return semanticColors['negative'];\n      case 'movement':\n      case 'transfer':\n        return semanticColors['info'];\n      case 'warning':\n        return semanticColors['warning'];\n      default:\n        return semanticColors['primary'];\n    }\n  }\n}\n"], "mappings": "AAEA,SAAqBA,eAAe,QAAQ,MAAM;AAClD,SAASC,WAAW,QAAQ,gCAAgC;;;AAsC5D,MAGaC,sBAAsB;EAKjCC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAJhB,KAAAC,OAAO,GAAWJ,WAAW,CAACK,SAAS;IACvC,KAAAC,aAAa,GAAG,IAAIP,eAAe,CAAyB,IAAI,CAAC;IAClE,KAAAQ,OAAO,GAAG,IAAI,CAACD,aAAa,CAACE,YAAY,EAAE;EAEX;EAEvC;;;EAGAC,UAAUA,CAAA;IACR,OAAO,IAAI,CAACN,IAAI,CAACO,GAAG,CAA0B,GAAG,IAAI,CAACN,OAAO,4BAA4B,CAAC;EAC5F;EAEA;;;EAGAO,SAASA,CAACC,MAAuB;IAC/B,IAAI,CAACN,aAAa,CAACO,IAAI,CAACD,MAAM,CAAC;EACjC;EAEA;;;EAGAE,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACR,aAAa,CAACS,KAAK;EACjC;EAEA;;;EAGAC,cAAcA,CAAA;IACZ,MAAMJ,MAAM,GAAG,IAAI,CAACE,gBAAgB,EAAE;IACtC,OAAOF,MAAM,EAAEK,YAAY,IAAI,EAAE;EACnC;EAEA;;;EAGAC,iBAAiBA,CAAA;IACf,MAAMN,MAAM,GAAG,IAAI,CAACE,gBAAgB,EAAE;IACtC,OAAOF,MAAM,EAAEO,QAAQ,EAAEC,MAAM,IAAI,GAAG;EACxC;EAEA;;;EAGAC,mBAAmBA,CAACC,QAAgB;IAClC,MAAMV,MAAM,GAAG,IAAI,CAACE,gBAAgB,EAAE;IACtC,OAAOF,MAAM,EAAEW,mBAAmB,EAAEC,MAAM,GAAGF,QAAQ,CAAC,IAAI,SAAS;EACrE;EAEA;;;EAGAG,kBAAkBA,CAACH,QAAgB;IACjC,MAAMV,MAAM,GAAG,IAAI,CAACE,gBAAgB,EAAE;IACtC,OAAOF,MAAM,EAAEW,mBAAmB,EAAEG,KAAK,GAAGJ,QAAQ,CAAC,IAAI,WAAW;EACtE;EAEA;;;EAGAK,sBAAsBA,CAAA;IACpB,MAAMf,MAAM,GAAG,IAAI,CAACE,gBAAgB,EAAE;IACtC,OAAOF,MAAM,EAAEgB,qBAAqB,IAAI;MACtCC,UAAU,EAAE,IAAI;MAChBC,mBAAmB,EAAE;KACtB;EACH;EAEA;;;EAGAC,iBAAiBA,CAAA;IACf,MAAMnB,MAAM,GAAG,IAAI,CAACE,gBAAgB,EAAE;IACtC,OAAOF,MAAM,EAAEoB,eAAe,IAAI,EAAE;EACtC;EAEA;;;EAGAC,kBAAkBA,CAAA;IAChB,MAAMrB,MAAM,GAAG,IAAI,CAACE,gBAAgB,EAAE;IACtC,OAAOF,MAAM,EAAEsB,iBAAiB,IAAI,EAAE;EACxC;EAEA;;;EAGAC,WAAWA,CAAA;IACT,MAAMvB,MAAM,GAAG,IAAI,CAACE,gBAAgB,EAAE;IACtC,OAAOF,MAAM,EAAEwB,SAAS,IAAI;MAC1BC,uBAAuB,EAAE,EAAE;MAC3BC,sBAAsB,EAAE,UAAU;MAClCC,iBAAiB,EAAE;KACpB;EACH;EAEA;;;EAGAC,uBAAuBA,CAACC,IAAY;IAClC,MAAM7B,MAAM,GAAG,IAAI,CAACE,gBAAgB,EAAE;IACtC,OAAOF,MAAM,EAAE8B,WAAW,GAAGD,IAAI,CAAC,IAAIA,IAAI;EAC5C;EAEA;;;EAGAE,iBAAiBA,CAACC,YAAiB;IACjC,MAAMC,cAAc,GAAG,IAAI,CAAClB,sBAAsB,EAAE;IACpD,OAAO,IAAI,CAACmB,SAAS,CAACD,cAAc,EAAED,YAAY,IAAI,EAAE,CAAC;EAC3D;EAEA;;;EAGQE,SAASA,CAACC,MAAW,EAAEC,MAAW;IACxC,MAAMC,MAAM,GAAG;MAAE,GAAGF;IAAM,CAAE;IAE5B,KAAK,MAAMG,GAAG,IAAIF,MAAM,EAAE;MACxB,IAAIA,MAAM,CAACE,GAAG,CAAC,IAAI,OAAOF,MAAM,CAACE,GAAG,CAAC,KAAK,QAAQ,IAAI,CAACC,KAAK,CAACC,OAAO,CAACJ,MAAM,CAACE,GAAG,CAAC,CAAC,EAAE;QACjFD,MAAM,CAACC,GAAG,CAAC,GAAG,IAAI,CAACJ,SAAS,CAACG,MAAM,CAACC,GAAG,CAAC,IAAI,EAAE,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;OAC7D,MAAM;QACLD,MAAM,CAACC,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;;;IAI7B,OAAOD,MAAM;EACf;EAEA;;;EAGAI,cAAcA,CAACtC,KAAa;IAC1B,MAAMK,MAAM,GAAG,IAAI,CAACF,iBAAiB,EAAE;IACvC,OAAO,GAAGE,MAAM,GAAGL,KAAK,CAACuC,cAAc,EAAE,EAAE;EAC7C;EAEA;;;EAGAC,iBAAiBA,CAACxC,KAAa;IAC7B,MAAMK,MAAM,GAAG,IAAI,CAACF,iBAAiB,EAAE;IAEvC,IAAIH,KAAK,IAAI,QAAQ,EAAE;MAAE;MACvB,OAAO,GAAGK,MAAM,GAAG,CAACL,KAAK,GAAG,QAAQ,EAAEyC,OAAO,CAAC,CAAC,CAAC,IAAI;KACrD,MAAM,IAAIzC,KAAK,IAAI,MAAM,EAAE;MAAE;MAC5B,OAAO,GAAGK,MAAM,GAAG,CAACL,KAAK,GAAG,MAAM,EAAEyC,OAAO,CAAC,CAAC,CAAC,GAAG;KAClD,MAAM,IAAIzC,KAAK,IAAI,IAAI,EAAE;MAAE;MAC1B,OAAO,GAAGK,MAAM,GAAG,CAACL,KAAK,GAAG,IAAI,EAAEyC,OAAO,CAAC,CAAC,CAAC,GAAG;KAChD,MAAM;MACL,OAAO,GAAGpC,MAAM,GAAGL,KAAK,CAACuC,cAAc,EAAE,EAAE;;EAE/C;EAEA;;;EAGAG,iBAAiBA,CAAA;IACf,OAAO;MACLC,QAAQ,EAAE,SAAS;MACnBC,QAAQ,EAAE,SAAS;MACnBC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,SAAS;MAClBC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,SAAS,CAAM;KACzB;EACH;EAEA;;;EAGAC,kBAAkBA,CAACjD,KAAa,EAAEkD,OAAA,GAAkB,SAAS;IAC3D,MAAMC,cAAc,GAAG,IAAI,CAACT,iBAAiB,EAAE;IAE/C,QAAQQ,OAAO;MACb,KAAK,QAAQ;MACb,KAAK,QAAQ;QACX,OAAOlD,KAAK,IAAI,CAAC,GAAGmD,cAAc,CAAC,UAAU,CAAC,GAAGA,cAAc,CAAC,UAAU,CAAC;MAC7E,KAAK,MAAM;MACX,KAAK,UAAU;QACb,OAAOA,cAAc,CAAC,UAAU,CAAC;MACnC,KAAK,UAAU;MACf,KAAK,UAAU;QACb,OAAOA,cAAc,CAAC,MAAM,CAAC;MAC/B,KAAK,SAAS;QACZ,OAAOA,cAAc,CAAC,SAAS,CAAC;MAClC;QACE,OAAOA,cAAc,CAAC,SAAS,CAAC;;EAEtC;;;uBAhMWjE,sBAAsB,EAAAkE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAtBrE,sBAAsB;MAAAsE,OAAA,EAAtBtE,sBAAsB,CAAAuE,IAAA;MAAAC,UAAA,EAFrB;IAAM;EAAA;;SAEPxE,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
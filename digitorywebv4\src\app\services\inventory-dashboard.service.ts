import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface InventoryDashboardFilters {
  locations: string[];
  startDate: string;
  endDate: string;
  categories?: string[];
  subCategories?: string[];
}

export interface InventoryDashboardRequest {
  tenant_id: string;
  filters: InventoryDashboardFilters;
  dashboard_type: string;
}

export interface InventorySummaryCard {
  icon: string;
  value: string;
  label: string;
  color: string;
  data_type: string;
}

export interface InventoryChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor: string[];
    borderColor: string[];
    borderWidth?: number;
    fill?: boolean;
    tension?: number;
  }[];
}

export interface InventoryChart {
  id: string;
  title: string;
  type: string;
  data: InventoryChartData;
}

export interface InventoryDashboardData {
  summary_cards: InventorySummaryCard[];
  charts: InventoryChart[];
  top_items: any[];
}

export interface InventoryDashboardResponse {
  status: string;
  data: InventoryDashboardData;
}

@Injectable({
  providedIn: 'root'
})
export class InventoryDashboardService {
  private baseUrl: string = environment.baseUrl;
  private engineUrl: string = environment.engineUrl;

  constructor(private http: HttpClient) {}

  /**
   * Get inventory dashboard data using store_variance function
   */
  getInventoryDashboardData(request: InventoryDashboardRequest): Observable<InventoryDashboardResponse> {
    return this.http.post<any>(`${this.baseUrl}getStoreVariance/`, request)
      .pipe(
        map(response => this.processStoreVarianceData(response))
      );
  }

  /**
   * Process store_variance data into dashboard format
   */
  private processStoreVarianceData(response: any): InventoryDashboardResponse {
    if (!response || !response.response || !response.response.store_variance) {
      return {
        status: 'error',
        data: {
          summary_cards: [],
          charts: [],
          top_items: []
        }
      };
    }

    const storeVarianceData = response.response.store_variance;
    
    return {
      status: 'success',
      data: {
        summary_cards: this.generateSummaryCards(storeVarianceData),
        charts: this.generateCharts(storeVarianceData),
        top_items: this.getTopInventoryItems(storeVarianceData)
      }
    };
  }

  /**
   * Generate summary cards from store variance data
   */
  private generateSummaryCards(data: any[]): InventorySummaryCard[] {
    if (!data || data.length === 0) return [];

    const totalItems = data.length;
    const totalOpeningValue = data.reduce((sum, item) => sum + (item['Opening Amount'] || 0), 0);
    const totalClosingValue = data.reduce((sum, item) => sum + (item['Closing Amount'] || 0), 0);
    const totalVarianceValue = data.reduce((sum, item) => sum + (item['Variance Amount'] || 0), 0);
    const totalPurchaseValue = data.reduce((sum, item) => sum + (item['Purchase Amount'] || 0), 0);
    
    // Calculate stockout items (items with zero closing stock)
    const stockoutItems = data.filter(item => (item['Closing Qty'] || 0) === 0).length;
    const stockoutPercentage = totalItems > 0 ? (stockoutItems / totalItems * 100).toFixed(1) : '0';
    
    // Calculate excess stock items (items with high variance)
    const excessItems = data.filter(item => (item['Variance'] || 0) > 0).length;
    const excessPercentage = totalItems > 0 ? (excessItems / totalItems * 100).toFixed(1) : '0';

    return [
      {
        icon: 'inventory',
        value: totalItems.toString(),
        label: 'Total Inventory Items',
        color: '#ffb366',
        data_type: 'number'
      },
      {
        icon: 'attach_money',
        value: this.formatCurrency(totalOpeningValue),
        label: 'Opening Inventory Value',
        color: '#ffc999',
        data_type: 'currency'
      },
      {
        icon: 'account_balance_wallet',
        value: this.formatCurrency(totalClosingValue),
        label: 'Current Inventory Value',
        color: '#ffab66',
        data_type: 'currency'
      },
      {
        icon: 'trending_down',
        value: this.formatCurrency(Math.abs(totalVarianceValue)),
        label: 'Total Variance Value',
        color: totalVarianceValue >= 0 ? '#90EE90' : '#FFB6C1',
        data_type: 'currency'
      },
      {
        icon: 'shopping_cart',
        value: this.formatCurrency(totalPurchaseValue),
        label: 'Purchase Value',
        color: '#ffd6b3',
        data_type: 'currency'
      },
      {
        icon: 'warning',
        value: `${stockoutPercentage}%`,
        label: 'Stockout Items',
        color: '#ff9999',
        data_type: 'percentage'
      }
    ];
  }

  /**
   * Generate charts from store variance data
   */
  private generateCharts(data: any[]): InventoryChart[] {
    if (!data || data.length === 0) return [];

    const charts: InventoryChart[] = [];

    // 1. Inventory Levels Gauge Chart (Top 10 items by value)
    charts.push(this.createInventoryLevelsChart(data));

    // 2. Inventory Turnover Rate Chart
    charts.push(this.createTurnoverRateChart(data));

    // 3. Stockout and Excess Stock Pie Chart
    charts.push(this.createStockoutExcessChart(data));

    // 4. Inventory Cost Trends Chart
    charts.push(this.createCostTrendsChart(data));

    // 5. Inventory by Category Chart
    charts.push(this.createInventoryByCategoryChart(data));

    // 6. Purchase vs Consumption Chart
    charts.push(this.createPurchaseConsumptionChart(data));

    return charts;
  }

  /**
   * Create inventory levels chart (bar chart of top items by closing value)
   */
  private createInventoryLevelsChart(data: any[]): InventoryChart {
    const sortedItems = data
      .filter(item => (item['Closing Amount'] || 0) > 0)
      .sort((a, b) => (b['Closing Amount'] || 0) - (a['Closing Amount'] || 0))
      .slice(0, 10);

    const labels = sortedItems.map(item => item['Item Name'] || 'Unknown');
    const values = sortedItems.map(item => item['Closing Amount'] || 0);
    const colors = this.getChartColors();

    return {
      id: 'inventory-levels',
      title: 'Current Inventory Levels (Top 10 by Value)',
      type: 'bar',
      data: {
        labels,
        datasets: [{
          label: 'Inventory Value',
          data: values,
          backgroundColor: colors,
          borderColor: colors,
          borderWidth: 1
        }]
      }
    };
  }

  /**
   * Create turnover rate chart (line chart showing opening vs closing quantities)
   */
  private createTurnoverRateChart(data: any[]): InventoryChart {
    const topItems = data
      .filter(item => (item['Opening Qty'] || 0) > 0 || (item['Closing Qty'] || 0) > 0)
      .sort((a, b) => (b['Opening Qty'] || 0) - (a['Opening Qty'] || 0))
      .slice(0, 8);

    const labels = topItems.map(item => item['Item Name'] || 'Unknown');
    const openingQty = topItems.map(item => item['Opening Qty'] || 0);
    const closingQty = topItems.map(item => item['Closing Qty'] || 0);

    return {
      id: 'turnover-rate',
      title: 'Inventory Turnover Rate (Opening vs Closing)',
      type: 'line',
      data: {
        labels,
        datasets: [
          {
            label: 'Opening Quantity',
            data: openingQty,
            backgroundColor: ['#ffb366'],
            borderColor: ['#ffb366'],
            borderWidth: 2,
            fill: false,
            tension: 0.3
          },
          {
            label: 'Closing Quantity',
            data: closingQty,
            backgroundColor: ['#ffc999'],
            borderColor: ['#ffc999'],
            borderWidth: 2,
            fill: false,
            tension: 0.3
          }
        ]
      }
    };
  }

  /**
   * Create stockout and excess stock pie chart
   */
  private createStockoutExcessChart(data: any[]): InventoryChart {
    const stockoutItems = data.filter(item => (item['Closing Qty'] || 0) === 0).length;
    const excessItems = data.filter(item => (item['Variance'] || 0) > 0).length;
    const normalItems = data.length - stockoutItems - excessItems;

    return {
      id: 'stockout-excess',
      title: 'Stock Status Distribution',
      type: 'doughnut',
      data: {
        labels: ['Normal Stock', 'Excess Stock', 'Stockout'],
        datasets: [{
          label: 'Items Count',
          data: [normalItems, excessItems, stockoutItems],
          backgroundColor: ['#90EE90', '#ffb366', '#ff9999'],
          borderColor: ['#90EE90', '#ffb366', '#ff9999'],
          borderWidth: 1
        }]
      }
    };
  }

  /**
   * Create cost trends chart (area chart)
   */
  private createCostTrendsChart(data: any[]): InventoryChart {
    const categories = [...new Set(data.map(item => item['Category'] || 'Unknown'))].slice(0, 6);
    const openingAmounts = categories.map(category => 
      data.filter(item => item['Category'] === category)
          .reduce((sum, item) => sum + (item['Opening Amount'] || 0), 0)
    );
    const closingAmounts = categories.map(category => 
      data.filter(item => item['Category'] === category)
          .reduce((sum, item) => sum + (item['Closing Amount'] || 0), 0)
    );

    return {
      id: 'cost-trends',
      title: 'Inventory Cost Trends by Category',
      type: 'line',
      data: {
        labels: categories,
        datasets: [
          {
            label: 'Opening Value',
            data: openingAmounts,
            backgroundColor: ['#ffb36620'],
            borderColor: ['#ffb366'],
            borderWidth: 2,
            fill: true,
            tension: 0.3
          },
          {
            label: 'Current Value',
            data: closingAmounts,
            backgroundColor: ['#ffc99920'],
            borderColor: ['#ffc999'],
            borderWidth: 2,
            fill: true,
            tension: 0.3
          }
        ]
      }
    };
  }

  /**
   * Create inventory by category chart
   */
  private createInventoryByCategoryChart(data: any[]): InventoryChart {
    const categoryData = data.reduce((acc, item) => {
      const category = item['Category'] || 'Unknown';
      if (!acc[category]) {
        acc[category] = 0;
      }
      acc[category] += item['Closing Amount'] || 0;
      return acc;
    }, {} as { [key: string]: number });

    const sortedCategories = Object.entries(categoryData)
      .sort(([,a], [,b]) => (b as number) - (a as number))
      .slice(0, 8);

    const labels = sortedCategories.map(([category]) => category);
    const values = sortedCategories.map(([, value]) => value as number);
    const colors = this.getChartColors();

    return {
      id: 'inventory-by-category',
      title: 'Inventory Value by Category',
      type: 'bar',
      data: {
        labels,
        datasets: [{
          label: 'Inventory Value',
          data: values,
          backgroundColor: colors,
          borderColor: colors,
          borderWidth: 1
        }]
      }
    };
  }

  /**
   * Create purchase vs consumption chart
   */
  private createPurchaseConsumptionChart(data: any[]): InventoryChart {
    const topItems = data
      .filter(item => (item['Purchase Qty'] || 0) > 0 || (item['Indent Qty'] || 0) > 0)
      .sort((a, b) => (b['Purchase Amount'] || 0) - (a['Purchase Amount'] || 0))
      .slice(0, 8);

    const labels = topItems.map(item => item['Item Name'] || 'Unknown');
    const purchaseQty = topItems.map(item => item['Purchase Qty'] || 0);
    const consumptionQty = topItems.map(item => item['Indent Qty'] || 0);

    return {
      id: 'purchase-consumption',
      title: 'Purchase vs Consumption Analysis',
      type: 'bar',
      data: {
        labels,
        datasets: [
          {
            label: 'Purchase Quantity',
            data: purchaseQty,
            backgroundColor: ['#ffb366'],
            borderColor: ['#ffb366'],
            borderWidth: 1
          },
          {
            label: 'Consumption Quantity',
            data: consumptionQty,
            backgroundColor: ['#ffc999'],
            borderColor: ['#ffc999'],
            borderWidth: 1
          }
        ]
      }
    };
  }

  /**
   * Get top inventory items for table display
   */
  private getTopInventoryItems(data: any[]): any[] {
    return data
      .sort((a, b) => (b['Closing Amount'] || 0) - (a['Closing Amount'] || 0))
      .slice(0, 15)
      .map(item => ({
        itemName: item['Item Name'] || 'Unknown',
        category: item['Category'] || 'Unknown',
        openingQty: item['Opening Qty'] || 0,
        closingQty: item['Closing Qty'] || 0,
        purchaseQty: item['Purchase Qty'] || 0,
        variance: item['Variance'] || 0,
        closingValue: item['Closing Amount'] || 0,
        uom: item['UOM'] || 'N/A'
      }));
  }

  /**
   * Get light orange theme colors for charts
   */
  private getChartColors(): string[] {
    return [
      '#ffb366', '#ffc999', '#ffab66', '#ffd6b3', '#ff9d4d',
      '#ffe0cc', '#ffb84d', '#fff2e6', '#ffa64d', '#fff5f0'
    ];
  }

  /**
   * Format currency value
   */
  private formatCurrency(value: number, currency: string = '₹'): string {
    if (value >= 10000000) { // 1 crore
      return `${currency}${(value / 10000000).toFixed(2)}Cr`;
    } else if (value >= 100000) { // 1 lakh
      return `${currency}${(value / 100000).toFixed(2)}L`;
    } else if (value >= 1000) { // 1 thousand
      return `${currency}${(value / 1000).toFixed(2)}K`;
    } else {
      return `${currency}${value.toFixed(2)}`;
    }
  }
}

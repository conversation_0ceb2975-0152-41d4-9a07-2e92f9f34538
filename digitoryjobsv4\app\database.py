from pymongo import MongoClient
from app.config import settings

client = MongoClient(settings.DATABASE_URL)
print(f'Connected to MongoDB- {settings.MONGO_INITDB_DATABASE}...')

db = client[settings.MONGO_INITDB_DATABASE]
shedular = db.email.shedular
Post = db.posts
Stockvalues = db.stockvalues
grnsCol = db.grns

Servingsizerecipes = db.servingsizerecipes
adjustinvCol=db.adjustinvlists
ibtsCol=db.ibts
tmpclosingsCol=db.tmpclosings
dailybackupsCol=db.dailybackups
menupriceCol=db.menuprice
menuidmappingsCol=db.menuidmappings
dailysalesdataCol=db.dailysalesdata
weightedaveragesCol=db.weightedaverages
tenantsCol=db.tenants
purchaseordersCol=db.purchaseorders
purchaserequestsCol=db.purchaserequests
intrabranchtransfersCol=db.intrabranchtransfers
servingsizerecipesCol=db.servingsizerecipes
deletedgrnsCol=db.deletedgrns
purchaseinvoicesCol=db.purchaseinvoices
indentlistsCol=db.indentlists
roloposconfigsCol=db.roloposconfigs
salesdetailsCol=db.salesdetails
metricsCol=db.metrics
mappingCol=db.menutoworkareamapping
report=db.reportlists
users=db.users
branchesCol=db.branches
tempMasterDataCol=db.temp_master_data
autoIncCol=db.auto_increment_counters
masterdataupdateconfigsCol=db.masterdataupdateconfigs
restinvcol = db['restaurantinventories']
productionsourcecol = db['productionsource']
categoriesCol = db['categories']
rts=db.rts
reportLists= db.reportlists
roloposconfigsCol= db.roloposconfigs
deletedposCol = db.deleted_pos
spoilages =db.spoilages
deletedindentsCol= db.deleted_indents
{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { NgChartsModule } from 'ng2-charts';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport { FormControl, ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/smart-dashboard.service\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"../../services/share-data.service\";\nimport * as i4 from \"../../services/dashboard-config.service\";\nimport * as i5 from \"../../services/chart-renderer.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/core\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/datepicker\";\nimport * as i15 from \"@angular/material/progress-spinner\";\nimport * as i16 from \"ng2-charts\";\nimport * as i17 from \"ngx-mat-select-search\";\nimport * as i18 from \"@angular/forms\";\nfunction SmartDashboardComponent_mat_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dashboardType_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", dashboardType_r8.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", dashboardType_r8.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const branch_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", branch_r9.restaurantIdOld);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", branch_r9.branchName, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baseDateOption_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", baseDateOption_r10.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", baseDateOption_r10.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_83_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵelement(1, \"mat-spinner\", 38);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading dashboard data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SmartDashboardComponent_div_84_div_1_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 44)(1, \"mat-card-content\")(2, \"div\", 45)(3, \"div\", 46)(4, \"mat-icon\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 47)(7, \"div\", 48);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 49);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const card_r14 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"border-left-color\", card_r14.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"color\", card_r14.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(card_r14.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(card_r14.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(card_r14.label);\n  }\n}\nfunction SmartDashboardComponent_div_84_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtemplate(1, SmartDashboardComponent_div_84_div_1_mat_card_1_Template, 11, 7, \"mat-card\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.summaryCards);\n  }\n}\nfunction SmartDashboardComponent_div_84_div_2_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 52)(1, \"mat-card-header\")(2, \"mat-card-title\", 53);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"div\", 54);\n    i0.ɵɵelement(6, \"canvas\", 55);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const chart_r16 = ctx.$implicit;\n    const ctx_r15 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r15.getChartCssClass(chart_r16));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(chart_r16.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"data-chart-type\", chart_r16.type);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"type\", ctx_r15.getChartType(chart_r16))(\"data\", ctx_r15.getChartData(chart_r16))(\"options\", ctx_r15.getChartOptions(chart_r16));\n  }\n}\nfunction SmartDashboardComponent_div_84_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtemplate(1, SmartDashboardComponent_div_84_div_2_mat_card_1_Template, 7, 6, \"mat-card\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.charts);\n  }\n}\nfunction SmartDashboardComponent_div_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtemplate(1, SmartDashboardComponent_div_84_div_1_Template, 2, 1, \"div\", 40);\n    i0.ɵɵtemplate(2, SmartDashboardComponent_div_84_div_2_Template, 2, 1, \"div\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.summaryCards.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.charts.length > 0);\n  }\n}\nfunction SmartDashboardComponent_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"mat-icon\", 57);\n    i0.ɵɵtext(2, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No Data Available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Please select a location and date range to view dashboard data.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function SmartDashboardComponent_div_85_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.loadDashboardData());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Refresh Data \");\n    i0.ɵɵelementEnd()();\n  }\n}\nclass SmartDashboardComponent {\n  constructor(smartDashboardService, authService, shareDataService, configService, chartRenderer, cdr) {\n    this.smartDashboardService = smartDashboardService;\n    this.authService = authService;\n    this.shareDataService = shareDataService;\n    this.configService = configService;\n    this.chartRenderer = chartRenderer;\n    this.cdr = cdr;\n    this.destroy$ = new Subject();\n    this.branches = [];\n    this.filteredBranches = [];\n    this.selectedLocation = null;\n    // Form controls\n    this.locationFilterCtrl = new FormControl('');\n    this.startDate = new FormControl();\n    this.endDate = new FormControl();\n    this.searchQuery = new FormControl('');\n    this.baseDateCtrl = new FormControl();\n    this.selectedDashboard = '';\n    // Dashboard data\n    this.summaryCards = [];\n    this.charts = [];\n    this.isLoading = false;\n    // Dynamic configuration data\n    this.dashboardTypes = [];\n    this.baseDateOptions = [];\n    this.user = this.authService.getCurrentUser();\n    this.initializeConfig();\n  }\n  initializeConfig() {\n    // Load dashboard configuration on component initialization\n    this.configService.loadConfig().subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.configService.setConfig(response.data);\n          this.setupDynamicConfigurations(response.data);\n        }\n      },\n      error: () => {\n        this.setupDefaultConfigurations();\n      }\n    });\n  }\n  setupDynamicConfigurations(config) {\n    // Set dashboard types\n    this.dashboardTypes = config.dashboard_types || [];\n    // Set base date options\n    this.baseDateOptions = config.base_date_options || [];\n    // Set default values from UI config\n    const uiConfig = config.ui_config || {};\n    const defaultDateRangeDays = uiConfig.default_date_range_days || 30;\n    // Set default form values\n    this.selectedDashboard = uiConfig.default_dashboard_type || 'purchase';\n    this.baseDateCtrl.setValue(uiConfig.default_base_date || 'deliveryDate');\n    this.startDate.setValue(new Date(Date.now() - defaultDateRangeDays * 24 * 60 * 60 * 1000));\n    this.endDate.setValue(new Date());\n  }\n  setupDefaultConfigurations() {\n    // Fallback configurations if backend fails\n    this.dashboardTypes = [{\n      value: 'purchase',\n      label: 'Purchase Dashboard'\n    }, {\n      value: 'inventory',\n      label: 'Inventory Dashboard'\n    }];\n    this.baseDateOptions = [{\n      value: 'deliveryDate',\n      label: 'Delivery Date'\n    }, {\n      value: 'orderDate',\n      label: 'Order Date'\n    }];\n    this.selectedDashboard = 'purchase';\n    this.baseDateCtrl.setValue('deliveryDate');\n    this.startDate.setValue(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000));\n    this.endDate.setValue(new Date());\n  }\n  ngOnInit() {\n    this.initializeFilters();\n    this.loadBranches();\n    this.loadDashboardData();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  initializeFilters() {\n    // Location filter\n    this.locationFilterCtrl.valueChanges.pipe(debounceTime(200), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(value => {\n      this.filterBranches(value || '');\n    });\n  }\n  loadBranches() {\n    this.shareDataService.selectedBranchesSource.pipe(takeUntil(this.destroy$)).subscribe(data => {\n      this.branches = data || [];\n      this.filteredBranches = [...this.branches];\n      if (this.branches.length === 1) {\n        this.selectedLocation = this.branches[0].restaurantIdOld;\n      }\n    });\n  }\n  filterBranches(searchTerm) {\n    if (!searchTerm) {\n      this.filteredBranches = [...this.branches];\n    } else {\n      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\\s/g, '');\n      this.filteredBranches = this.branches.filter(branch => branch.branchName.toLowerCase().replace(/\\s/g, '').includes(normalizedSearchTerm));\n    }\n  }\n  loadDashboardData() {\n    if (!this.startDate.value || !this.endDate.value) {\n      return;\n    }\n    this.isLoading = true;\n    const filters = {\n      locations: this.selectedLocation ? [this.selectedLocation] : [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: '',\n      use_default_charts: true,\n      dashboard_type: this.selectedDashboard\n    };\n    this.smartDashboardService.getSmartDashboardData(request).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.processDashboardData(response.data);\n        } else {\n          console.warn('API returned no data');\n          this.clearDashboardData();\n        }\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading dashboard data:', error);\n        this.clearDashboardData();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  clearDashboardData() {\n    this.summaryCards = [];\n    this.charts = [];\n  }\n  processDashboardData(data) {\n    // Process summary cards using config service\n    this.summaryCards = data.summary_items?.map(item => ({\n      icon: item.icon || this.configService.getSummaryCardIcon(item.data_type),\n      value: item.value,\n      label: item.label,\n      color: this.configService.getSummaryCardColor(item.data_type),\n      data_type: item.data_type\n    })) || [];\n    // Process charts using chart renderer service\n    this.charts = data.charts?.map(chart => this.chartRenderer.processChart(chart)) || [];\n  }\n  formatDate(date) {\n    return date.toISOString().split('T')[0];\n  }\n  onLocationChange() {\n    this.loadDashboardData();\n  }\n  onDateChange() {\n    this.loadDashboardData();\n  }\n  onDashboardChange() {\n    this.loadDashboardData();\n  }\n  onSearchQuery() {\n    const query = this.searchQuery.value?.trim();\n    if (query) {\n      this.loadDashboardDataWithQuery(query);\n    } else {\n      this.loadDashboardData();\n    }\n  }\n  loadDashboardDataWithQuery(query) {\n    if (!this.startDate.value || !this.endDate.value) {\n      return;\n    }\n    this.isLoading = true;\n    const filters = {\n      locations: this.selectedLocation ? [this.selectedLocation] : [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: query,\n      use_default_charts: false,\n      dashboard_type: this.selectedDashboard\n    };\n    this.smartDashboardService.getSmartDashboardData(request).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.processDashboardData(response.data);\n        } else {\n          console.warn('Query returned no data');\n          this.clearDashboardData();\n        }\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Error with query:', error);\n        this.clearDashboardData();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  // Dynamic chart methods using services\n  getChartData(chart) {\n    return chart.data;\n  }\n  getChartType(chart) {\n    return this.chartRenderer.getChartType(chart.type);\n  }\n  getChartOptions(chart) {\n    return chart.options || this.configService.getDefaultChartOptions();\n  }\n  getChartCssClass(chart) {\n    return this.chartRenderer.getChartCssClass(chart);\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)(i0.ɵɵdirectiveInject(i1.SmartDashboardService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ShareDataService), i0.ɵɵdirectiveInject(i4.DashboardConfigService), i0.ɵɵdirectiveInject(i5.ChartRendererService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 86,\n      vars: 17,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"main-layout\"], [1, \"left-sidebar\"], [1, \"dashboard-selection\"], [\"appearance\", \"outline\", 1, \"dashboard-dropdown\"], [3, \"value\", \"valueChange\", \"selectionChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"filters-section\"], [1, \"filters-title\"], [1, \"filter-count\"], [1, \"filter-group\"], [1, \"filter-label\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [\"placeholderLabel\", \"Search locations...\", \"noEntriesFoundLabel\", \"No locations found\", 3, \"formControl\"], [3, \"formControl\", \"selectionChange\"], [\"matInput\", \"\", 3, \"matDatepicker\", \"formControl\", \"dateChange\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [\"endPicker\", \"\"], [1, \"filter-actions\"], [\"mat-stroked-button\", \"\", 1, \"reset-filters-btn\", 3, \"click\"], [1, \"right-content\"], [1, \"search-header\"], [1, \"assistant-info\"], [1, \"assistant-icon\"], [1, \"assistant-text\"], [1, \"assistant-title\"], [1, \"assistant-status\"], [1, \"search-container\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"placeholder\", \"Ask me about your business data\", 3, \"formControl\", \"keyup.enter\"], [\"matSuffix\", \"\", 1, \"search-icon\", 3, \"click\"], [1, \"dashboard-content-area\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"dashboard-grid\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [3, \"value\"], [1, \"loading-container\"], [\"diameter\", \"50\"], [1, \"dashboard-grid\"], [\"class\", \"summary-cards-row\", 4, \"ngIf\"], [\"class\", \"charts-grid\", 4, \"ngIf\"], [1, \"summary-cards-row\"], [\"class\", \"summary-card\", 3, \"border-left-color\", 4, \"ngFor\", \"ngForOf\"], [1, \"summary-card\"], [1, \"card-content\"], [1, \"card-icon\"], [1, \"card-info\"], [1, \"card-value\"], [1, \"card-label\"], [1, \"charts-grid\"], [\"class\", \"chart-card\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"chart-card\", 3, \"ngClass\"], [1, \"chart-title\"], [1, \"chart-container\"], [\"baseChart\", \"\", 3, \"type\", \"data\", \"options\"], [1, \"empty-state\"], [1, \"empty-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-form-field\", 4)(5, \"mat-label\");\n          i0.ɵɵtext(6, \"Select Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-select\", 5);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_7_listener($event) {\n            return ctx.selectedDashboard = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_7_listener() {\n            return ctx.onDashboardChange();\n          });\n          i0.ɵɵtemplate(8, SmartDashboardComponent_mat_option_8_Template, 2, 2, \"mat-option\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"h3\", 8)(11, \"mat-icon\");\n          i0.ɵɵtext(12, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(13, \" Smart Filters \");\n          i0.ɵɵelementStart(14, \"span\", 9);\n          i0.ɵɵtext(15, \"1\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 10)(17, \"h4\", 11)(18, \"mat-icon\");\n          i0.ɵɵtext(19, \"restaurant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(20, \" Restaurants \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"mat-form-field\", 12)(22, \"mat-label\");\n          i0.ɵɵtext(23, \"Select restaurants\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"mat-select\", 5);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_24_listener($event) {\n            return ctx.selectedLocation = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_24_listener() {\n            return ctx.onLocationChange();\n          });\n          i0.ɵɵelementStart(25, \"mat-option\");\n          i0.ɵɵelement(26, \"ngx-mat-select-search\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(27, SmartDashboardComponent_mat_option_27_Template, 2, 2, \"mat-option\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"div\", 10)(29, \"h4\", 11)(30, \"mat-icon\");\n          i0.ɵɵtext(31, \"event\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(32, \" Base Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"mat-form-field\", 12)(34, \"mat-label\");\n          i0.ɵɵtext(35, \"Select base date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"mat-select\", 14);\n          i0.ɵɵlistener(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_36_listener() {\n            return ctx.onDateChange();\n          });\n          i0.ɵɵtemplate(37, SmartDashboardComponent_mat_option_37_Template, 2, 2, \"mat-option\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(38, \"div\", 10)(39, \"h4\", 11)(40, \"mat-icon\");\n          i0.ɵɵtext(41, \"date_range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(42, \" Start Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"mat-form-field\", 12)(44, \"mat-label\");\n          i0.ɵɵtext(45, \"Start Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"input\", 15);\n          i0.ɵɵlistener(\"dateChange\", function SmartDashboardComponent_Template_input_dateChange_46_listener() {\n            return ctx.onDateChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(47, \"mat-datepicker-toggle\", 16)(48, \"mat-datepicker\", null, 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"div\", 10)(51, \"h4\", 11)(52, \"mat-icon\");\n          i0.ɵɵtext(53, \"date_range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(54, \" End Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"mat-form-field\", 12)(56, \"mat-label\");\n          i0.ɵɵtext(57, \"End Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"input\", 15);\n          i0.ɵɵlistener(\"dateChange\", function SmartDashboardComponent_Template_input_dateChange_58_listener() {\n            return ctx.onDateChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(59, \"mat-datepicker-toggle\", 16)(60, \"mat-datepicker\", null, 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(62, \"div\", 19)(63, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_63_listener() {\n            return ctx.loadDashboardData();\n          });\n          i0.ɵɵelementStart(64, \"mat-icon\");\n          i0.ɵɵtext(65, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(66, \" Reset filters \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(67, \"div\", 21)(68, \"div\", 22)(69, \"div\", 23)(70, \"mat-icon\", 24);\n          i0.ɵɵtext(71, \"smart_toy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"div\", 25)(73, \"span\", 26);\n          i0.ɵɵtext(74, \"Smart Dashboard Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"span\", 27);\n          i0.ɵɵtext(76, \"Ready to analyze\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(77, \"div\", 28)(78, \"mat-form-field\", 29)(79, \"input\", 30);\n          i0.ɵɵlistener(\"keyup.enter\", function SmartDashboardComponent_Template_input_keyup_enter_79_listener() {\n            return ctx.onSearchQuery();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"mat-icon\", 31);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_mat_icon_click_80_listener() {\n            return ctx.onSearchQuery();\n          });\n          i0.ɵɵtext(81, \"search\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(82, \"div\", 32);\n          i0.ɵɵtemplate(83, SmartDashboardComponent_div_83_Template, 4, 0, \"div\", 33);\n          i0.ɵɵtemplate(84, SmartDashboardComponent_div_84_Template, 3, 2, \"div\", 34);\n          i0.ɵɵtemplate(85, SmartDashboardComponent_div_85_Template, 11, 0, \"div\", 35);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r3 = i0.ɵɵreference(49);\n          const _r4 = i0.ɵɵreference(61);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"value\", ctx.selectedDashboard);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.dashboardTypes);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"value\", ctx.selectedLocation);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formControl\", ctx.locationFilterCtrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredBranches);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"formControl\", ctx.baseDateCtrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.baseDateOptions);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"matDatepicker\", _r3)(\"formControl\", ctx.startDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r3);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"matDatepicker\", _r4)(\"formControl\", ctx.endDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r4);\n          i0.ɵɵadvance(20);\n          i0.ɵɵproperty(\"formControl\", ctx.searchQuery);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && (ctx.summaryCards.length > 0 || ctx.charts.length > 0));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.summaryCards.length === 0 && ctx.charts.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i6.NgClass, i6.NgForOf, i6.NgIf, MatCardModule, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, MatButtonModule, i8.MatButton, MatIconModule, i9.MatIcon, MatSelectModule, i10.MatFormField, i10.MatLabel, i10.MatSuffix, i11.MatSelect, i12.MatOption, MatFormFieldModule, MatInputModule, i13.MatInput, MatDatepickerModule, i14.MatDatepicker, i14.MatDatepickerInput, i14.MatDatepickerToggle, MatNativeDateModule, MatDividerModule, MatProgressSpinnerModule, i15.MatProgressSpinner, NgChartsModule, i16.BaseChartDirective, NgxMatSelectSearchModule, i17.MatSelectSearchComponent, ReactiveFormsModule, i18.DefaultValueAccessor, i18.NgControlStatus, i18.FormControlDirective, FormsModule],\n      styles: [\".smart-dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  background-color: #f8f9fa;\\n  height: 100vh;\\n  overflow: hidden;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-text-field-wrapper {\\n  height: 36px !important;\\n  min-height: 36px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-text-field-wrapper .mat-mdc-form-field-infix {\\n  padding: 6px 12px !important;\\n  min-height: 24px !important;\\n  border-top: none !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-text-field-wrapper .mat-mdc-form-field-flex {\\n  align-items: center !important;\\n  height: 36px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-form-field-subscript-wrapper {\\n  display: none !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-form-field-outline {\\n  color: #dee2e6 !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-form-field-outline-thick {\\n  color: #ffb366 !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-form-field-label {\\n  color: #666 !important;\\n  font-size: 13px !important;\\n  top: 18px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-form-field.mat-focused .mat-mdc-form-field-label {\\n  color: #ffb366 !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-form-field.mat-form-field-should-float .mat-mdc-form-field-label {\\n  transform: translateY(-12px) scale(0.75) !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-select .mat-mdc-select-trigger {\\n  height: 36px !important;\\n  display: flex !important;\\n  align-items: center !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-select .mat-mdc-select-value {\\n  font-size: 13px !important;\\n  line-height: 24px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-select .mat-mdc-select-arrow {\\n  color: #ffb366 !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-select-panel .mat-mdc-option {\\n  height: 32px !important;\\n  line-height: 32px !important;\\n  font-size: 13px !important;\\n  padding: 0 16px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-select-panel .mat-mdc-option.mat-mdc-option-active {\\n  background: rgba(255, 179, 102, 0.1) !important;\\n  color: #ffb366 !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-select-panel .mat-mdc-option:hover {\\n  background: rgba(255, 179, 102, 0.05) !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-input-element {\\n  font-size: 13px !important;\\n  height: 24px !important;\\n  line-height: 24px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-datepicker-toggle .mat-icon {\\n  color: #ffb366 !important;\\n  font-size: 18px !important;\\n  width: 18px !important;\\n  height: 18px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-datepicker-content .mat-calendar-header {\\n  background: #ffb366 !important;\\n  color: white !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-datepicker-content .mat-calendar-body-selected {\\n  background-color: #ffb366 !important;\\n  color: white !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-datepicker-content .mat-calendar-body-today:not(.mat-calendar-body-selected) {\\n  border-color: #ffb366 !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-raised-button, .smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-outlined-button {\\n  height: 32px !important;\\n  line-height: 32px !important;\\n  padding: 0 12px !important;\\n  font-size: 13px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-raised-button.mat-primary, .smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-outlined-button.mat-primary {\\n  background-color: #ffb366 !important;\\n  color: white !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-raised-button.mat-primary:hover, .smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-outlined-button.mat-primary:hover {\\n  background-color: #ffa64d !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-outlined-button.mat-primary {\\n  border-color: #ffb366 !important;\\n  color: #ffb366 !important;\\n  background-color: transparent !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-outlined-button.mat-primary:hover {\\n  background-color: rgba(255, 179, 102, 0.05) !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: calc(100vh - 40px);\\n  overflow: hidden;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%] {\\n  width: 240px;\\n  background: linear-gradient(135deg, #ffffff 0%, #fff5f0 100%);\\n  border-right: 1px solid #ffe0cc;\\n  padding: 16px;\\n  box-shadow: 2px 0 4px rgba(255, 179, 102, 0.08);\\n  height: 100%;\\n  overflow-y: auto;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #fff2e6;\\n  border-radius: 3px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #ffb366;\\n  border-radius: 3px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #ffa64d;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  padding: 0;\\n  background: none;\\n  border-radius: 0;\\n  position: relative;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);\\n  border-radius: 6px;\\n  border: 1px solid #e9ecef;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper:hover {\\n  border-color: #ffb366;\\n  box-shadow: 0 2px 6px rgba(255, 179, 102, 0.1);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-form-field-outline {\\n  display: none;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-form-field-outline-thick {\\n  display: none;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-form-field-label {\\n  color: #666;\\n  font-weight: 500;\\n  font-size: 12px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-select-value {\\n  color: #333;\\n  font-weight: 600;\\n  font-size: 14px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-select-arrow {\\n  color: #ffb366;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-form-field-infix {\\n  padding: 8px 12px;\\n  min-height: 36px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-select-trigger {\\n  height: 36px;\\n  display: flex;\\n  align-items: center;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-form-field-flex {\\n  align-items: center;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-focused .mat-mdc-text-field-wrapper {\\n  border-color: #ffb366 !important;\\n  box-shadow: 0 3px 8px rgba(255, 179, 102, 0.15) !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 0 16px 0;\\n  font-size: 15px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #ffb366;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-title[_ngcontent-%COMP%]   .filter-count[_ngcontent-%COMP%] {\\n  background: #ffb366;\\n  color: white;\\n  border-radius: 50%;\\n  width: 18px;\\n  height: 18px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 10px;\\n  font-weight: bold;\\n  margin-left: auto;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%] {\\n  margin-bottom: 10px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  margin: 0 0 8px 0;\\n  font-size: 13px;\\n  font-weight: 500;\\n  color: #333;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  width: 14px;\\n  height: 14px;\\n  color: #ffb366;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 6px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  background-color: white;\\n  border-radius: 4px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-outline {\\n  color: #dee2e6;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-outline-thick {\\n  color: #ffb366;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-label {\\n  font-size: 13px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  padding-top: 16px;\\n  border-top: 1px solid #e9ecef;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%]   .reset-filters-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  color: #6c757d;\\n  border-color: #dee2e6;\\n  font-weight: 500;\\n  padding: 8px;\\n  border-radius: 4px;\\n  font-size: 13px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%]   .reset-filters-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  border-color: #adb5bd;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  background-color: #f8f9fa;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);\\n  border-bottom: 1px solid #e9ecef;\\n  padding: 10px 20px;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  min-height: 50px;\\n  box-shadow: 0 1px 3px rgba(255, 179, 102, 0.05);\\n  overflow: hidden;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .assistant-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  flex-shrink: 0;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .assistant-info[_ngcontent-%COMP%]   .assistant-icon[_ngcontent-%COMP%] {\\n  color: #ffb366;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .assistant-info[_ngcontent-%COMP%]   .assistant-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .assistant-info[_ngcontent-%COMP%]   .assistant-text[_ngcontent-%COMP%]   .assistant-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #333;\\n  white-space: nowrap;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .assistant-info[_ngcontent-%COMP%]   .assistant-text[_ngcontent-%COMP%]   .assistant-status[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: #28a745;\\n  font-weight: 500;\\n  background: #e8f5e8;\\n  padding: 2px 8px;\\n  border-radius: 12px;\\n  display: inline-block;\\n  white-space: nowrap;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n  max-width: calc(100% - 200px);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 100%;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  background-color: white;\\n  border-radius: 24px;\\n  height: 36px;\\n  border: 1px solid #e9ecef;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\\n  transition: all 0.3s ease;\\n  width: 100%;\\n  max-width: 100%;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper:hover {\\n  border-color: #ffb366;\\n  box-shadow: 0 4px 12px rgba(255, 179, 102, 0.1);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-form-field-outline {\\n  display: none;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-form-field-outline-thick {\\n  display: none;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-form-field-infix {\\n  padding: 6px 16px;\\n  border-top: none;\\n  width: 100%;\\n  max-width: 100%;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-form-field-flex {\\n  align-items: center;\\n  height: 36px;\\n  width: 100%;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     input {\\n  font-size: 14px;\\n  color: #333;\\n  font-weight: 400;\\n  width: 100%;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     input::placeholder {\\n  color: #999;\\n  font-size: 14px;\\n  font-weight: 400;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-focused .mat-mdc-text-field-wrapper {\\n  border-color: #ffb366 !important;\\n  box-shadow: 0 4px 16px rgba(255, 179, 102, 0.15) !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%] {\\n  color: #999;\\n  cursor: pointer;\\n  font-size: 16px;\\n  transition: color 0.2s ease;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%]:hover {\\n  color: #ffb366;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  flex: 1;\\n  overflow-y: auto;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #ffb366;\\n  border-radius: 3px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #ffa64d;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 400px;\\n  gap: 16px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 16px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(4, 1fr);\\n  gap: 16px;\\n  margin-bottom: 20px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  border: 1px solid #e9ecef;\\n  transition: box-shadow 0.2s ease;\\n  position: relative;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]     .mat-mdc-card-content {\\n  padding: 16px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: linear-gradient(135deg, #ffb366 0%, #ffc999 100%);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n  color: white;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-info[_ngcontent-%COMP%]   .card-value[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 4px;\\n  line-height: 1.2;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-info[_ngcontent-%COMP%]   .card-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #6c757d;\\n  font-weight: 500;\\n  line-height: 1.3;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(12, 1fr);\\n  gap: 16px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  border: 1px solid #e9ecef;\\n  transition: box-shadow 0.2s ease;\\n  height: 400px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 12px rgba(255, 179, 102, 0.1);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card.full-width[_ngcontent-%COMP%] {\\n  grid-column: span 12;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card.half-width[_ngcontent-%COMP%] {\\n  grid-column: span 6;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card.third-width[_ngcontent-%COMP%] {\\n  grid-column: span 4;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #333;\\n  margin: 0;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 320px;\\n  padding: 16px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   canvas[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n  height: 100% !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 280px;\\n  color: #adb5bd;\\n  text-align: center;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 40px;\\n  width: 40px;\\n  height: 40px;\\n  margin-bottom: 8px;\\n  color: #ffb366;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 13px;\\n  font-weight: 500;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 400px;\\n  text-align: center;\\n  color: #666;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  width: 64px;\\n  height: 64px;\\n  margin-bottom: 16px;\\n  opacity: 0.5;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 20px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  font-size: 14px;\\n}\\n\\n@media (max-width: 1400px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card.half-width[_ngcontent-%COMP%], .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card.third-width[_ngcontent-%COMP%] {\\n    grid-column: span 12;\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n    align-items: stretch;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%] {\\n    width: 220px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    border-right: none;\\n    border-bottom: 1px solid #e9ecef;\\n    padding: 12px;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n    padding: 12px 16px;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 12px;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%] {\\n    grid-column: span 12 !important;\\n  }\\n}\\n  .chart-container canvas {\\n  max-width: 100% !important;\\n  height: auto !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatSelectModule", "MatFormFieldModule", "MatInputModule", "MatDatepickerModule", "MatNativeDateModule", "MatDividerModule", "MatProgressSpinnerModule", "NgChartsModule", "NgxMatSelectSearchModule", "FormControl", "ReactiveFormsModule", "FormsModule", "Subject", "takeUntil", "debounceTime", "distinctUntilChanged", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "dashboardType_r8", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "branch_r9", "restaurantIdOld", "branchName", "baseDateOption_r10", "ɵɵelement", "ɵɵstyleProp", "card_r14", "color", "ɵɵtextInterpolate", "icon", "ɵɵtemplate", "SmartDashboardComponent_div_84_div_1_mat_card_1_Template", "ctx_r11", "summaryCards", "ctx_r15", "getChartCssClass", "chart_r16", "title", "ɵɵattribute", "type", "getChartType", "getChartData", "getChartOptions", "SmartDashboardComponent_div_84_div_2_mat_card_1_Template", "ctx_r12", "charts", "SmartDashboardComponent_div_84_div_1_Template", "SmartDashboardComponent_div_84_div_2_Template", "ctx_r6", "length", "ɵɵlistener", "SmartDashboardComponent_div_85_Template_button_click_7_listener", "ɵɵrestoreView", "_r19", "ctx_r18", "ɵɵnextContext", "ɵɵresetView", "loadDashboardData", "SmartDashboardComponent", "constructor", "smartDashboardService", "authService", "shareDataService", "configService", "<PERSON><PERSON><PERSON><PERSON>", "cdr", "destroy$", "branches", "filteredBranches", "selectedLocation", "locationFilterCtrl", "startDate", "endDate", "searchQuery", "baseDateCtrl", "selectedDashboard", "isLoading", "dashboardTypes", "baseDateOptions", "user", "getCurrentUser", "initializeConfig", "loadConfig", "subscribe", "next", "response", "status", "setConfig", "data", "setupDynamicConfigurations", "error", "setupDefaultConfigurations", "config", "dashboard_types", "base_date_options", "uiConfig", "ui_config", "defaultDateRangeDays", "default_date_range_days", "default_dashboard_type", "setValue", "default_base_date", "Date", "now", "ngOnInit", "initializeFilters", "loadBranches", "ngOnDestroy", "complete", "valueChanges", "pipe", "filterBranches", "selectedBranchesSource", "searchTerm", "normalizedSearchTerm", "toLowerCase", "replace", "filter", "branch", "includes", "filters", "locations", "formatDate", "baseDate", "request", "tenant_id", "tenantId", "user_query", "use_default_charts", "dashboard_type", "getSmartDashboardData", "processDashboardData", "console", "warn", "clearDashboardData", "detectChanges", "summary_items", "map", "item", "getSummaryCardIcon", "data_type", "getSummaryCardColor", "chart", "processChart", "date", "toISOString", "split", "onLocationChange", "onDateChange", "onDashboardChange", "onSearchQuery", "query", "trim", "loadDashboardDataWithQuery", "options", "getDefaultChartOptions", "ɵɵdirectiveInject", "i1", "SmartDashboardService", "i2", "AuthService", "i3", "ShareDataService", "i4", "DashboardConfigService", "i5", "ChartRendererService", "ChangeDetectorRef", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SmartDashboardComponent_Template", "rf", "ctx", "SmartDashboardComponent_Template_mat_select_valueChange_7_listener", "$event", "SmartDashboardComponent_Template_mat_select_selectionChange_7_listener", "SmartDashboardComponent_mat_option_8_Template", "SmartDashboardComponent_Template_mat_select_valueChange_24_listener", "SmartDashboardComponent_Template_mat_select_selectionChange_24_listener", "SmartDashboardComponent_mat_option_27_Template", "SmartDashboardComponent_Template_mat_select_selectionChange_36_listener", "SmartDashboardComponent_mat_option_37_Template", "SmartDashboardComponent_Template_input_dateChange_46_listener", "SmartDashboardComponent_Template_input_dateChange_58_listener", "SmartDashboardComponent_Template_button_click_63_listener", "SmartDashboardComponent_Template_input_keyup_enter_79_listener", "SmartDashboardComponent_Template_mat_icon_click_80_listener", "SmartDashboardComponent_div_83_Template", "SmartDashboardComponent_div_84_Template", "SmartDashboardComponent_div_85_Template", "_r3", "_r4", "i6", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i8", "MatButton", "i9", "MatIcon", "i10", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i11", "MatSelect", "i12", "MatOption", "i13", "MatInput", "i14", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i15", "MatProgressSpinner", "i16", "BaseChartDirective", "i17", "MatSelectSearchComponent", "i18", "DefaultValueAccessor", "NgControlStatus", "FormControlDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\digii\\digitorywebv4\\src\\app\\pages\\smart-dashboard\\smart-dashboard.component.ts", "C:\\Users\\<USER>\\Desktop\\digii\\digitorywebv4\\src\\app\\pages\\smart-dashboard\\smart-dashboard.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { NgChartsModule } from 'ng2-charts';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport { FormControl, ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { ChartConfiguration, ChartData, ChartType } from 'chart.js';\n\nimport { SmartDashboardService } from '../../services/smart-dashboard.service';\nimport { AuthService } from '../../services/auth.service';\nimport { ShareDataService } from '../../services/share-data.service';\nimport { DashboardConfigService, DashboardType, BaseDateOption } from '../../services/dashboard-config.service';\nimport { ChartRendererService, ChartModel } from '../../services/chart-renderer.service';\n\ninterface SummaryCard {\n  icon: string;\n  value: string;\n  label: string;\n  color: string;\n  data_type?: string;\n}\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatSelectModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatDividerModule,\n    MatProgressSpinnerModule,\n    NgChartsModule,\n    NgxMatSelectSearchModule,\n    ReactiveFormsModule,\n    FormsModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n  \n  // User and branch data\n  user: any;\n  branches: any[] = [];\n  filteredBranches: any[] = [];\n  selectedLocation: string | null = null;\n  \n  // Form controls\n  locationFilterCtrl = new FormControl('');\n  startDate = new FormControl();\n  endDate = new FormControl();\n  searchQuery = new FormControl('');\n  baseDateCtrl = new FormControl();\n  selectedDashboard = '';\n\n  // Dashboard data\n  summaryCards: SummaryCard[] = [];\n  charts: ChartModel[] = [];\n  isLoading = false;\n\n  // Dynamic configuration data\n  dashboardTypes: DashboardType[] = [];\n  baseDateOptions: BaseDateOption[] = [];\n\n\n\n\n\n  constructor(\n    private smartDashboardService: SmartDashboardService,\n    private authService: AuthService,\n    private shareDataService: ShareDataService,\n    private configService: DashboardConfigService,\n    private chartRenderer: ChartRendererService,\n    private cdr: ChangeDetectorRef\n  ) {\n    this.user = this.authService.getCurrentUser();\n    this.initializeConfig();\n  }\n\n  private initializeConfig(): void {\n    // Load dashboard configuration on component initialization\n    this.configService.loadConfig().subscribe({\n      next: (response) => {\n        if (response.status === 'success') {\n          this.configService.setConfig(response.data);\n          this.setupDynamicConfigurations(response.data);\n        }\n      },\n      error: () => {\n        this.setupDefaultConfigurations();\n      }\n    });\n  }\n\n  private setupDynamicConfigurations(config: any): void {\n    // Set dashboard types\n    this.dashboardTypes = config.dashboard_types || [];\n\n    // Set base date options\n    this.baseDateOptions = config.base_date_options || [];\n\n    // Set default values from UI config\n    const uiConfig = config.ui_config || {};\n    const defaultDateRangeDays = uiConfig.default_date_range_days || 30;\n\n    // Set default form values\n    this.selectedDashboard = uiConfig.default_dashboard_type || 'purchase';\n    this.baseDateCtrl.setValue(uiConfig.default_base_date || 'deliveryDate');\n    this.startDate.setValue(new Date(Date.now() - defaultDateRangeDays * 24 * 60 * 60 * 1000));\n    this.endDate.setValue(new Date());\n  }\n\n  private setupDefaultConfigurations(): void {\n    // Fallback configurations if backend fails\n    this.dashboardTypes = [\n      { value: 'purchase', label: 'Purchase Dashboard' },\n      { value: 'inventory', label: 'Inventory Dashboard' }\n    ];\n    this.baseDateOptions = [\n      { value: 'deliveryDate', label: 'Delivery Date' },\n      { value: 'orderDate', label: 'Order Date' }\n    ];\n    this.selectedDashboard = 'purchase';\n    this.baseDateCtrl.setValue('deliveryDate');\n    this.startDate.setValue(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000));\n    this.endDate.setValue(new Date());\n  }\n\n  ngOnInit(): void {\n    this.initializeFilters();\n    this.loadBranches();\n    this.loadDashboardData();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private initializeFilters(): void {\n    // Location filter\n    this.locationFilterCtrl.valueChanges\n      .pipe(\n        debounceTime(200),\n        distinctUntilChanged(),\n        takeUntil(this.destroy$)\n      )\n      .subscribe((value: string | null) => {\n        this.filterBranches(value || '');\n      });\n  }\n\n  private loadBranches(): void {\n    this.shareDataService.selectedBranchesSource\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(data => {\n        this.branches = data || [];\n        this.filteredBranches = [...this.branches];\n        \n        if (this.branches.length === 1) {\n          this.selectedLocation = this.branches[0].restaurantIdOld;\n        }\n      });\n  }\n\n  private filterBranches(searchTerm: string): void {\n    if (!searchTerm) {\n      this.filteredBranches = [...this.branches];\n    } else {\n      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\\s/g, '');\n      this.filteredBranches = this.branches.filter(branch =>\n        branch.branchName.toLowerCase().replace(/\\s/g, '').includes(normalizedSearchTerm)\n      );\n    }\n  }\n\n  loadDashboardData(): void {\n    if (!this.startDate.value || !this.endDate.value) {\n      return;\n    }\n\n    this.isLoading = true;\n\n    const filters = {\n      locations: this.selectedLocation ? [this.selectedLocation] : [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: '',\n      use_default_charts: true,\n      dashboard_type: this.selectedDashboard\n    };\n\n    this.smartDashboardService.getSmartDashboardData(request)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (response) => {\n          if (response.status === 'success') {\n            this.processDashboardData(response.data);\n          } else {\n            console.warn('API returned no data');\n            this.clearDashboardData();\n          }\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        },\n        error: (error) => {\n          console.error('Error loading dashboard data:', error);\n          this.clearDashboardData();\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        }\n      });\n  }\n\n  private clearDashboardData(): void {\n    this.summaryCards = [];\n    this.charts = [];\n  }\n\n  private processDashboardData(data: any): void {\n    // Process summary cards using config service\n    this.summaryCards = data.summary_items?.map((item: any) => ({\n      icon: item.icon || this.configService.getSummaryCardIcon(item.data_type),\n      value: item.value,\n      label: item.label,\n      color: this.configService.getSummaryCardColor(item.data_type),\n      data_type: item.data_type\n    })) || [];\n\n    // Process charts using chart renderer service\n    this.charts = data.charts?.map((chart: any) =>\n      this.chartRenderer.processChart(chart)\n    ) || [];\n  }\n\n\n\n\n\n  private formatDate(date: Date): string {\n    return date.toISOString().split('T')[0];\n  }\n\n  onLocationChange(): void {\n    this.loadDashboardData();\n  }\n\n  onDateChange(): void {\n    this.loadDashboardData();\n  }\n\n  onDashboardChange(): void {\n    this.loadDashboardData();\n  }\n\n  onSearchQuery(): void {\n    const query = this.searchQuery.value?.trim();\n    if (query) {\n      this.loadDashboardDataWithQuery(query);\n    } else {\n      this.loadDashboardData();\n    }\n  }\n\n  private loadDashboardDataWithQuery(query: string): void {\n    if (!this.startDate.value || !this.endDate.value) {\n      return;\n    }\n\n    this.isLoading = true;\n\n    const filters = {\n      locations: this.selectedLocation ? [this.selectedLocation] : [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: query,\n      use_default_charts: false,\n      dashboard_type: this.selectedDashboard\n    };\n\n    this.smartDashboardService.getSmartDashboardData(request)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (response) => {\n          if (response.status === 'success') {\n            this.processDashboardData(response.data);\n          } else {\n            console.warn('Query returned no data');\n            this.clearDashboardData();\n          }\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        },\n        error: (error) => {\n          console.error('Error with query:', error);\n          this.clearDashboardData();\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        }\n      });\n  }\n\n  // Dynamic chart methods using services\n  getChartData(chart: ChartModel): ChartData {\n    return chart.data;\n  }\n\n  getChartType(chart: ChartModel): ChartType {\n    return this.chartRenderer.getChartType(chart.type);\n  }\n\n  getChartOptions(chart: ChartModel): ChartConfiguration['options'] {\n    return chart.options || this.configService.getDefaultChartOptions();\n  }\n\n  getChartCssClass(chart: ChartModel): string {\n    return this.chartRenderer.getChartCssClass(chart);\n  }\n\n\n}\n", "<div class=\"smart-dashboard-container\">\n  <!-- Main Layout -->\n  <div class=\"main-layout\">\n    <!-- Left Sidebar -->\n    <div class=\"left-sidebar\">\n      <!-- Dashboard Selection -->\n      <div class=\"dashboard-selection\">\n        <mat-form-field appearance=\"outline\" class=\"dashboard-dropdown\">\n          <mat-label>Select Dashboard</mat-label>\n          <mat-select [(value)]=\"selectedDashboard\" (selectionChange)=\"onDashboardChange()\">\n            <mat-option *ngFor=\"let dashboardType of dashboardTypes\" [value]=\"dashboardType.value\">\n              {{dashboardType.label}}\n            </mat-option>\n          </mat-select>\n        </mat-form-field>\n      </div>\n      <!-- Smart Filters Section -->\n      <div class=\"filters-section\">\n        <h3 class=\"filters-title\">\n          <mat-icon>tune</mat-icon>\n          Smart Filters\n          <span class=\"filter-count\">1</span>\n        </h3>\n\n        <!-- Restaurants Filter -->\n        <div class=\"filter-group\">\n          <h4 class=\"filter-label\">\n            <mat-icon>restaurant</mat-icon>\n            Restaurants\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>Select restaurants</mat-label>\n            <mat-select [(value)]=\"selectedLocation\" (selectionChange)=\"onLocationChange()\">\n              <mat-option>\n                <ngx-mat-select-search\n                  [formControl]=\"locationFilterCtrl\"\n                  placeholderLabel=\"Search locations...\"\n                  noEntriesFoundLabel=\"No locations found\">\n                </ngx-mat-select-search>\n              </mat-option>\n              <mat-option *ngFor=\"let branch of filteredBranches\" [value]=\"branch.restaurantIdOld\">\n                {{branch.branchName}}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Base Date Filter -->\n        <div class=\"filter-group\">\n          <h4 class=\"filter-label\">\n            <mat-icon>event</mat-icon>\n            Base Date\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>Select base date</mat-label>\n            <mat-select [formControl]=\"baseDateCtrl\" (selectionChange)=\"onDateChange()\">\n              <mat-option *ngFor=\"let baseDateOption of baseDateOptions\" [value]=\"baseDateOption.value\">\n                {{baseDateOption.label}}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Start Date Filter -->\n        <div class=\"filter-group\">\n          <h4 class=\"filter-label\">\n            <mat-icon>date_range</mat-icon>\n            Start Date\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>Start Date</mat-label>\n            <input matInput [matDatepicker]=\"startPicker\" [formControl]=\"startDate\" (dateChange)=\"onDateChange()\">\n            <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #startPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- End Date Filter -->\n        <div class=\"filter-group\">\n          <h4 class=\"filter-label\">\n            <mat-icon>date_range</mat-icon>\n            End Date\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>End Date</mat-label>\n            <input matInput [matDatepicker]=\"endPicker\" [formControl]=\"endDate\" (dateChange)=\"onDateChange()\">\n            <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #endPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- Reset Filters Button -->\n        <div class=\"filter-actions\">\n          <button mat-stroked-button class=\"reset-filters-btn\" (click)=\"loadDashboardData()\">\n            <mat-icon>refresh</mat-icon>\n            Reset filters\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Right Content Area -->\n    <div class=\"right-content\">\n      <!-- Top Search Bar -->\n      <div class=\"search-header\">\n        <div class=\"assistant-info\">\n          <mat-icon class=\"assistant-icon\">smart_toy</mat-icon>\n          <div class=\"assistant-text\">\n            <span class=\"assistant-title\">Smart Dashboard Assistant</span>\n            <span class=\"assistant-status\">Ready to analyze</span>\n          </div>\n        </div>\n        <div class=\"search-container\">\n          <mat-form-field appearance=\"outline\" class=\"search-field\">\n            <input matInput\n                   placeholder=\"Ask me about your business data\"\n                   [formControl]=\"searchQuery\"\n                   (keyup.enter)=\"onSearchQuery()\" />\n            <mat-icon matSuffix class=\"search-icon\" (click)=\"onSearchQuery()\">search</mat-icon>\n          </mat-form-field>\n        </div>\n      </div>\n\n      <!-- Dashboard Content Area -->\n      <div class=\"dashboard-content-area\">\n        <!-- Loading Spinner -->\n        <div *ngIf=\"isLoading\" class=\"loading-container\">\n          <mat-spinner diameter=\"50\"></mat-spinner>\n          <p>Loading dashboard data...</p>\n        </div>\n\n        <!-- Dashboard Grid -->\n        <div *ngIf=\"!isLoading && (summaryCards.length > 0 || charts.length > 0)\" class=\"dashboard-grid\">\n          <!-- Summary Cards Row -->\n          <div *ngIf=\"summaryCards.length > 0\" class=\"summary-cards-row\">\n            <mat-card *ngFor=\"let card of summaryCards\" class=\"summary-card\" [style.border-left-color]=\"card.color\">\n              <mat-card-content>\n                <div class=\"card-content\">\n                  <div class=\"card-icon\" [style.color]=\"card.color\">\n                    <mat-icon>{{card.icon}}</mat-icon>\n                  </div>\n                  <div class=\"card-info\">\n                    <div class=\"card-value\">{{card.value}}</div>\n                    <div class=\"card-label\">{{card.label}}</div>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </div>\n\n          <!-- Charts Grid -->\n          <div *ngIf=\"charts.length > 0\" class=\"charts-grid\">\n            <mat-card *ngFor=\"let chart of charts; let i = index\"\n                      class=\"chart-card\"\n                      [ngClass]=\"getChartCssClass(chart)\">\n              <mat-card-header>\n                <mat-card-title class=\"chart-title\">{{chart.title}}</mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"chart-container\" [attr.data-chart-type]=\"chart.type\">\n                  <!-- Dynamic Chart Rendering -->\n                  <canvas baseChart\n                          [type]=\"getChartType(chart)\"\n                          [data]=\"getChartData(chart)\"\n                          [options]=\"getChartOptions(chart)\">\n                  </canvas>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </div>\n        </div>\n\n        <!-- Single Unified Empty State -->\n        <div *ngIf=\"!isLoading && summaryCards.length === 0 && charts.length === 0\" class=\"empty-state\">\n          <mat-icon class=\"empty-icon\">analytics</mat-icon>\n          <h3>No Data Available</h3>\n          <p>Please select a location and date range to view dashboard data.</p>\n          <button mat-raised-button color=\"primary\" (click)=\"loadDashboardData()\">\n            <mat-icon>refresh</mat-icon>\n            Refresh Data\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,cAAc,QAAQ,YAAY;AAC3C,SAASC,wBAAwB,QAAQ,uBAAuB;AAChE,SAASC,WAAW,EAAEC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAC9E,SAASC,OAAO,EAAEC,SAAS,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;ICLjEC,EAAA,CAAAC,cAAA,qBAAuF;IACrFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF4CH,EAAA,CAAAI,UAAA,UAAAC,gBAAA,CAAAC,KAAA,CAA6B;IACpFN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,gBAAA,CAAAI,KAAA,MACF;;;;;IA4BET,EAAA,CAAAC,cAAA,qBAAqF;IACnFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFuCH,EAAA,CAAAI,UAAA,UAAAM,SAAA,CAAAC,eAAA,CAAgC;IAClFX,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,SAAA,CAAAE,UAAA,MACF;;;;;IAcAZ,EAAA,CAAAC,cAAA,qBAA0F;IACxFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF8CH,EAAA,CAAAI,UAAA,UAAAS,kBAAA,CAAAP,KAAA,CAA8B;IACvFN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAK,kBAAA,CAAAJ,KAAA,MACF;;;;;IAoENT,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAc,SAAA,sBAAyC;IACzCd,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAO9BH,EAAA,CAAAC,cAAA,mBAAwG;IAItFD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEpCH,EAAA,CAAAC,cAAA,cAAuB;IACGD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5CH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IARaH,EAAA,CAAAe,WAAA,sBAAAC,QAAA,CAAAC,KAAA,CAAsC;IAG1EjB,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAe,WAAA,UAAAC,QAAA,CAAAC,KAAA,CAA0B;IACrCjB,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAAkB,iBAAA,CAAAF,QAAA,CAAAG,IAAA,CAAa;IAGCnB,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAkB,iBAAA,CAAAF,QAAA,CAAAV,KAAA,CAAc;IACdN,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAkB,iBAAA,CAAAF,QAAA,CAAAP,KAAA,CAAc;;;;;IAThDT,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAoB,UAAA,IAAAC,wDAAA,wBAYW;IACbrB,EAAA,CAAAG,YAAA,EAAM;;;;IAbuBH,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAI,UAAA,YAAAkB,OAAA,CAAAC,YAAA,CAAe;;;;;IAiB1CvB,EAAA,CAAAC,cAAA,mBAE8C;IAEND,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAEtEH,EAAA,CAAAC,cAAA,uBAAkB;IAGdD,EAAA,CAAAc,SAAA,iBAIS;IACXd,EAAA,CAAAG,YAAA,EAAM;;;;;IAZAH,EAAA,CAAAI,UAAA,YAAAoB,OAAA,CAAAC,gBAAA,CAAAC,SAAA,EAAmC;IAEL1B,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAkB,iBAAA,CAAAQ,SAAA,CAAAC,KAAA,CAAe;IAGtB3B,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAA4B,WAAA,oBAAAF,SAAA,CAAAG,IAAA,CAAmC;IAGtD7B,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAI,UAAA,SAAAoB,OAAA,CAAAM,YAAA,CAAAJ,SAAA,EAA4B,SAAAF,OAAA,CAAAO,YAAA,CAAAL,SAAA,cAAAF,OAAA,CAAAQ,eAAA,CAAAN,SAAA;;;;;IAX5C1B,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAoB,UAAA,IAAAa,wDAAA,uBAgBW;IACbjC,EAAA,CAAAG,YAAA,EAAM;;;;IAjBwBH,EAAA,CAAAO,SAAA,GAAW;IAAXP,EAAA,CAAAI,UAAA,YAAA8B,OAAA,CAAAC,MAAA,CAAW;;;;;IApB3CnC,EAAA,CAAAC,cAAA,cAAiG;IAE/FD,EAAA,CAAAoB,UAAA,IAAAgB,6CAAA,kBAcM;IAGNpC,EAAA,CAAAoB,UAAA,IAAAiB,6CAAA,kBAkBM;IACRrC,EAAA,CAAAG,YAAA,EAAM;;;;IApCEH,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAI,UAAA,SAAAkC,MAAA,CAAAf,YAAA,CAAAgB,MAAA,KAA6B;IAiB7BvC,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAI,UAAA,SAAAkC,MAAA,CAAAH,MAAA,CAAAI,MAAA,KAAuB;;;;;;IAsB/BvC,EAAA,CAAAC,cAAA,cAAgG;IACjED,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,sEAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtEH,EAAA,CAAAC,cAAA,iBAAwE;IAA9BD,EAAA,CAAAwC,UAAA,mBAAAC,gEAAA;MAAAzC,EAAA,CAAA0C,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA5C,EAAA,CAAA6C,aAAA;MAAA,OAAS7C,EAAA,CAAA8C,WAAA,CAAAF,OAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACrE/C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADpJnB,MAuBa6C,uBAAuB;EA8BlCC,YACUC,qBAA4C,EAC5CC,WAAwB,EACxBC,gBAAkC,EAClCC,aAAqC,EACrCC,aAAmC,EACnCC,GAAsB;IALtB,KAAAL,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,GAAG,GAAHA,GAAG;IAnCL,KAAAC,QAAQ,GAAG,IAAI5D,OAAO,EAAQ;IAItC,KAAA6D,QAAQ,GAAU,EAAE;IACpB,KAAAC,gBAAgB,GAAU,EAAE;IAC5B,KAAAC,gBAAgB,GAAkB,IAAI;IAEtC;IACA,KAAAC,kBAAkB,GAAG,IAAInE,WAAW,CAAC,EAAE,CAAC;IACxC,KAAAoE,SAAS,GAAG,IAAIpE,WAAW,EAAE;IAC7B,KAAAqE,OAAO,GAAG,IAAIrE,WAAW,EAAE;IAC3B,KAAAsE,WAAW,GAAG,IAAItE,WAAW,CAAC,EAAE,CAAC;IACjC,KAAAuE,YAAY,GAAG,IAAIvE,WAAW,EAAE;IAChC,KAAAwE,iBAAiB,GAAG,EAAE;IAEtB;IACA,KAAA1C,YAAY,GAAkB,EAAE;IAChC,KAAAY,MAAM,GAAiB,EAAE;IACzB,KAAA+B,SAAS,GAAG,KAAK;IAEjB;IACA,KAAAC,cAAc,GAAoB,EAAE;IACpC,KAAAC,eAAe,GAAqB,EAAE;IAcpC,IAAI,CAACC,IAAI,GAAG,IAAI,CAAClB,WAAW,CAACmB,cAAc,EAAE;IAC7C,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEQA,gBAAgBA,CAAA;IACtB;IACA,IAAI,CAAClB,aAAa,CAACmB,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAACvB,aAAa,CAACwB,SAAS,CAACF,QAAQ,CAACG,IAAI,CAAC;UAC3C,IAAI,CAACC,0BAA0B,CAACJ,QAAQ,CAACG,IAAI,CAAC;;MAElD,CAAC;MACDE,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACC,0BAA0B,EAAE;MACnC;KACD,CAAC;EACJ;EAEQF,0BAA0BA,CAACG,MAAW;IAC5C;IACA,IAAI,CAACf,cAAc,GAAGe,MAAM,CAACC,eAAe,IAAI,EAAE;IAElD;IACA,IAAI,CAACf,eAAe,GAAGc,MAAM,CAACE,iBAAiB,IAAI,EAAE;IAErD;IACA,MAAMC,QAAQ,GAAGH,MAAM,CAACI,SAAS,IAAI,EAAE;IACvC,MAAMC,oBAAoB,GAAGF,QAAQ,CAACG,uBAAuB,IAAI,EAAE;IAEnE;IACA,IAAI,CAACvB,iBAAiB,GAAGoB,QAAQ,CAACI,sBAAsB,IAAI,UAAU;IACtE,IAAI,CAACzB,YAAY,CAAC0B,QAAQ,CAACL,QAAQ,CAACM,iBAAiB,IAAI,cAAc,CAAC;IACxE,IAAI,CAAC9B,SAAS,CAAC6B,QAAQ,CAAC,IAAIE,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAGN,oBAAoB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAC1F,IAAI,CAACzB,OAAO,CAAC4B,QAAQ,CAAC,IAAIE,IAAI,EAAE,CAAC;EACnC;EAEQX,0BAA0BA,CAAA;IAChC;IACA,IAAI,CAACd,cAAc,GAAG,CACpB;MAAE7D,KAAK,EAAE,UAAU;MAAEG,KAAK,EAAE;IAAoB,CAAE,EAClD;MAAEH,KAAK,EAAE,WAAW;MAAEG,KAAK,EAAE;IAAqB,CAAE,CACrD;IACD,IAAI,CAAC2D,eAAe,GAAG,CACrB;MAAE9D,KAAK,EAAE,cAAc;MAAEG,KAAK,EAAE;IAAe,CAAE,EACjD;MAAEH,KAAK,EAAE,WAAW;MAAEG,KAAK,EAAE;IAAY,CAAE,CAC5C;IACD,IAAI,CAACwD,iBAAiB,GAAG,UAAU;IACnC,IAAI,CAACD,YAAY,CAAC0B,QAAQ,CAAC,cAAc,CAAC;IAC1C,IAAI,CAAC7B,SAAS,CAAC6B,QAAQ,CAAC,IAAIE,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IACxE,IAAI,CAAC/B,OAAO,CAAC4B,QAAQ,CAAC,IAAIE,IAAI,EAAE,CAAC;EACnC;EAEAE,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACjD,iBAAiB,EAAE;EAC1B;EAEAkD,WAAWA,CAAA;IACT,IAAI,CAACzC,QAAQ,CAACkB,IAAI,EAAE;IACpB,IAAI,CAAClB,QAAQ,CAAC0C,QAAQ,EAAE;EAC1B;EAEQH,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAACnC,kBAAkB,CAACuC,YAAY,CACjCC,IAAI,CACHtG,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBF,SAAS,CAAC,IAAI,CAAC2D,QAAQ,CAAC,CACzB,CACAiB,SAAS,CAAEnE,KAAoB,IAAI;MAClC,IAAI,CAAC+F,cAAc,CAAC/F,KAAK,IAAI,EAAE,CAAC;IAClC,CAAC,CAAC;EACN;EAEQ0F,YAAYA,CAAA;IAClB,IAAI,CAAC5C,gBAAgB,CAACkD,sBAAsB,CACzCF,IAAI,CAACvG,SAAS,CAAC,IAAI,CAAC2D,QAAQ,CAAC,CAAC,CAC9BiB,SAAS,CAACK,IAAI,IAAG;MAChB,IAAI,CAACrB,QAAQ,GAAGqB,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACpB,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;MAE1C,IAAI,IAAI,CAACA,QAAQ,CAAClB,MAAM,KAAK,CAAC,EAAE;QAC9B,IAAI,CAACoB,gBAAgB,GAAG,IAAI,CAACF,QAAQ,CAAC,CAAC,CAAC,CAAC9C,eAAe;;IAE5D,CAAC,CAAC;EACN;EAEQ0F,cAAcA,CAACE,UAAkB;IACvC,IAAI,CAACA,UAAU,EAAE;MACf,IAAI,CAAC7C,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;KAC3C,MAAM;MACL,MAAM+C,oBAAoB,GAAGD,UAAU,CAACE,WAAW,EAAE,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MACxE,IAAI,CAAChD,gBAAgB,GAAG,IAAI,CAACD,QAAQ,CAACkD,MAAM,CAACC,MAAM,IACjDA,MAAM,CAAChG,UAAU,CAAC6F,WAAW,EAAE,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACG,QAAQ,CAACL,oBAAoB,CAAC,CAClF;;EAEL;EAEAzD,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACc,SAAS,CAACvD,KAAK,IAAI,CAAC,IAAI,CAACwD,OAAO,CAACxD,KAAK,EAAE;MAChD;;IAGF,IAAI,CAAC4D,SAAS,GAAG,IAAI;IAErB,MAAM4C,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAACpD,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB,CAAC,GAAG,EAAE;MAC/DE,SAAS,EAAE,IAAI,CAACmD,UAAU,CAAC,IAAI,CAACnD,SAAS,CAACvD,KAAK,CAAC;MAChDwD,OAAO,EAAE,IAAI,CAACkD,UAAU,CAAC,IAAI,CAAClD,OAAO,CAACxD,KAAK,CAAC;MAC5C2G,QAAQ,EAAE,IAAI,CAACjD,YAAY,CAAC1D,KAAK,IAAI;KACtC;IAED,MAAM4G,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAAC9C,IAAI,CAAC+C,QAAQ;MAC7BN,OAAO,EAAEA,OAAO;MAChBO,UAAU,EAAE,EAAE;MACdC,kBAAkB,EAAE,IAAI;MACxBC,cAAc,EAAE,IAAI,CAACtD;KACtB;IAED,IAAI,CAACf,qBAAqB,CAACsE,qBAAqB,CAACN,OAAO,CAAC,CACtDd,IAAI,CAACvG,SAAS,CAAC,IAAI,CAAC2D,QAAQ,CAAC,CAAC,CAC9BiB,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAAC6C,oBAAoB,CAAC9C,QAAQ,CAACG,IAAI,CAAC;SACzC,MAAM;UACL4C,OAAO,CAACC,IAAI,CAAC,sBAAsB,CAAC;UACpC,IAAI,CAACC,kBAAkB,EAAE;;QAE3B,IAAI,CAAC1D,SAAS,GAAG,KAAK;QACtB,IAAI,CAACX,GAAG,CAACsE,aAAa,EAAE;MAC1B,CAAC;MACD7C,KAAK,EAAGA,KAAK,IAAI;QACf0C,OAAO,CAAC1C,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAAC4C,kBAAkB,EAAE;QACzB,IAAI,CAAC1D,SAAS,GAAG,KAAK;QACtB,IAAI,CAACX,GAAG,CAACsE,aAAa,EAAE;MAC1B;KACD,CAAC;EACN;EAEQD,kBAAkBA,CAAA;IACxB,IAAI,CAACrG,YAAY,GAAG,EAAE;IACtB,IAAI,CAACY,MAAM,GAAG,EAAE;EAClB;EAEQsF,oBAAoBA,CAAC3C,IAAS;IACpC;IACA,IAAI,CAACvD,YAAY,GAAGuD,IAAI,CAACgD,aAAa,EAAEC,GAAG,CAAEC,IAAS,KAAM;MAC1D7G,IAAI,EAAE6G,IAAI,CAAC7G,IAAI,IAAI,IAAI,CAACkC,aAAa,CAAC4E,kBAAkB,CAACD,IAAI,CAACE,SAAS,CAAC;MACxE5H,KAAK,EAAE0H,IAAI,CAAC1H,KAAK;MACjBG,KAAK,EAAEuH,IAAI,CAACvH,KAAK;MACjBQ,KAAK,EAAE,IAAI,CAACoC,aAAa,CAAC8E,mBAAmB,CAACH,IAAI,CAACE,SAAS,CAAC;MAC7DA,SAAS,EAAEF,IAAI,CAACE;KACjB,CAAC,CAAC,IAAI,EAAE;IAET;IACA,IAAI,CAAC/F,MAAM,GAAG2C,IAAI,CAAC3C,MAAM,EAAE4F,GAAG,CAAEK,KAAU,IACxC,IAAI,CAAC9E,aAAa,CAAC+E,YAAY,CAACD,KAAK,CAAC,CACvC,IAAI,EAAE;EACT;EAMQpB,UAAUA,CAACsB,IAAU;IAC3B,OAAOA,IAAI,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACzC;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAAC1F,iBAAiB,EAAE;EAC1B;EAEA2F,YAAYA,CAAA;IACV,IAAI,CAAC3F,iBAAiB,EAAE;EAC1B;EAEA4F,iBAAiBA,CAAA;IACf,IAAI,CAAC5F,iBAAiB,EAAE;EAC1B;EAEA6F,aAAaA,CAAA;IACX,MAAMC,KAAK,GAAG,IAAI,CAAC9E,WAAW,CAACzD,KAAK,EAAEwI,IAAI,EAAE;IAC5C,IAAID,KAAK,EAAE;MACT,IAAI,CAACE,0BAA0B,CAACF,KAAK,CAAC;KACvC,MAAM;MACL,IAAI,CAAC9F,iBAAiB,EAAE;;EAE5B;EAEQgG,0BAA0BA,CAACF,KAAa;IAC9C,IAAI,CAAC,IAAI,CAAChF,SAAS,CAACvD,KAAK,IAAI,CAAC,IAAI,CAACwD,OAAO,CAACxD,KAAK,EAAE;MAChD;;IAGF,IAAI,CAAC4D,SAAS,GAAG,IAAI;IAErB,MAAM4C,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAACpD,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB,CAAC,GAAG,EAAE;MAC/DE,SAAS,EAAE,IAAI,CAACmD,UAAU,CAAC,IAAI,CAACnD,SAAS,CAACvD,KAAK,CAAC;MAChDwD,OAAO,EAAE,IAAI,CAACkD,UAAU,CAAC,IAAI,CAAClD,OAAO,CAACxD,KAAK,CAAC;MAC5C2G,QAAQ,EAAE,IAAI,CAACjD,YAAY,CAAC1D,KAAK,IAAI;KACtC;IAED,MAAM4G,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAAC9C,IAAI,CAAC+C,QAAQ;MAC7BN,OAAO,EAAEA,OAAO;MAChBO,UAAU,EAAEwB,KAAK;MACjBvB,kBAAkB,EAAE,KAAK;MACzBC,cAAc,EAAE,IAAI,CAACtD;KACtB;IAED,IAAI,CAACf,qBAAqB,CAACsE,qBAAqB,CAACN,OAAO,CAAC,CACtDd,IAAI,CAACvG,SAAS,CAAC,IAAI,CAAC2D,QAAQ,CAAC,CAAC,CAC9BiB,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAAC6C,oBAAoB,CAAC9C,QAAQ,CAACG,IAAI,CAAC;SACzC,MAAM;UACL4C,OAAO,CAACC,IAAI,CAAC,wBAAwB,CAAC;UACtC,IAAI,CAACC,kBAAkB,EAAE;;QAE3B,IAAI,CAAC1D,SAAS,GAAG,KAAK;QACtB,IAAI,CAACX,GAAG,CAACsE,aAAa,EAAE;MAC1B,CAAC;MACD7C,KAAK,EAAGA,KAAK,IAAI;QACf0C,OAAO,CAAC1C,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QACzC,IAAI,CAAC4C,kBAAkB,EAAE;QACzB,IAAI,CAAC1D,SAAS,GAAG,KAAK;QACtB,IAAI,CAACX,GAAG,CAACsE,aAAa,EAAE;MAC1B;KACD,CAAC;EACN;EAEA;EACA9F,YAAYA,CAACqG,KAAiB;IAC5B,OAAOA,KAAK,CAACtD,IAAI;EACnB;EAEAhD,YAAYA,CAACsG,KAAiB;IAC5B,OAAO,IAAI,CAAC9E,aAAa,CAACxB,YAAY,CAACsG,KAAK,CAACvG,IAAI,CAAC;EACpD;EAEAG,eAAeA,CAACoG,KAAiB;IAC/B,OAAOA,KAAK,CAACY,OAAO,IAAI,IAAI,CAAC3F,aAAa,CAAC4F,sBAAsB,EAAE;EACrE;EAEAxH,gBAAgBA,CAAC2G,KAAiB;IAChC,OAAO,IAAI,CAAC9E,aAAa,CAAC7B,gBAAgB,CAAC2G,KAAK,CAAC;EACnD;;;uBApSWpF,uBAAuB,EAAAhD,EAAA,CAAAkJ,iBAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAApJ,EAAA,CAAAkJ,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAtJ,EAAA,CAAAkJ,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAxJ,EAAA,CAAAkJ,iBAAA,CAAAO,EAAA,CAAAC,sBAAA,GAAA1J,EAAA,CAAAkJ,iBAAA,CAAAS,EAAA,CAAAC,oBAAA,GAAA5J,EAAA,CAAAkJ,iBAAA,CAAAlJ,EAAA,CAAA6J,iBAAA;IAAA;EAAA;;;YAAvB7G,uBAAuB;MAAA8G,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAhK,EAAA,CAAAiK,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvDpCvK,EAAA,CAAAC,cAAA,aAAuC;UAQlBD,EAAA,CAAAE,MAAA,uBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,oBAAkF;UAAtED,EAAA,CAAAwC,UAAA,yBAAAiI,mEAAAC,MAAA;YAAA,OAAAF,GAAA,CAAAvG,iBAAA,GAAAyG,MAAA;UAAA,EAA6B,6BAAAC,uEAAA;YAAA,OAAoBH,GAAA,CAAA7B,iBAAA,EAAmB;UAAA,EAAvC;UACvC3I,EAAA,CAAAoB,UAAA,IAAAwJ,6CAAA,wBAEa;UACf5K,EAAA,CAAAG,YAAA,EAAa;UAIjBH,EAAA,CAAAC,cAAA,aAA6B;UAEfD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAE,MAAA,uBACA;UAAAF,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIrCH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,qBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACzCH,EAAA,CAAAC,cAAA,qBAAgF;UAApED,EAAA,CAAAwC,UAAA,yBAAAqI,oEAAAH,MAAA;YAAA,OAAAF,GAAA,CAAA7G,gBAAA,GAAA+G,MAAA;UAAA,EAA4B,6BAAAI,wEAAA;YAAA,OAAoBN,GAAA,CAAA/B,gBAAA,EAAkB;UAAA,EAAtC;UACtCzI,EAAA,CAAAC,cAAA,kBAAY;UACVD,EAAA,CAAAc,SAAA,iCAIwB;UAC1Bd,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAoB,UAAA,KAAA2J,8CAAA,wBAEa;UACf/K,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,sBAA4E;UAAnCD,EAAA,CAAAwC,UAAA,6BAAAwI,wEAAA;YAAA,OAAmBR,GAAA,CAAA9B,YAAA,EAAc;UAAA,EAAC;UACzE1I,EAAA,CAAAoB,UAAA,KAAA6J,8CAAA,wBAEa;UACfjL,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACjCH,EAAA,CAAAC,cAAA,iBAAsG;UAA9BD,EAAA,CAAAwC,UAAA,wBAAA0I,8DAAA;YAAA,OAAcV,GAAA,CAAA9B,YAAA,EAAc;UAAA,EAAC;UAArG1I,EAAA,CAAAG,YAAA,EAAsG;UACtGH,EAAA,CAAAc,SAAA,iCAA6E;UAE/Ed,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAC,cAAA,iBAAkG;UAA9BD,EAAA,CAAAwC,UAAA,wBAAA2I,8DAAA;YAAA,OAAcX,GAAA,CAAA9B,YAAA,EAAc;UAAA,EAAC;UAAjG1I,EAAA,CAAAG,YAAA,EAAkG;UAClGH,EAAA,CAAAc,SAAA,iCAA2E;UAE7Ed,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAA4B;UAC2BD,EAAA,CAAAwC,UAAA,mBAAA4I,0DAAA;YAAA,OAASZ,GAAA,CAAAzH,iBAAA,EAAmB;UAAA,EAAC;UAChF/C,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAA2B;UAIYD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrDH,EAAA,CAAAC,cAAA,eAA4B;UACID,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9DH,EAAA,CAAAC,cAAA,gBAA+B;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAG1DH,EAAA,CAAAC,cAAA,eAA8B;UAKnBD,EAAA,CAAAwC,UAAA,yBAAA6I,+DAAA;YAAA,OAAeb,GAAA,CAAA5B,aAAA,EAAe;UAAA,EAAC;UAHtC5I,EAAA,CAAAG,YAAA,EAGyC;UACzCH,EAAA,CAAAC,cAAA,oBAAkE;UAA1BD,EAAA,CAAAwC,UAAA,mBAAA8I,4DAAA;YAAA,OAASd,GAAA,CAAA5B,aAAA,EAAe;UAAA,EAAC;UAAC5I,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAMzFH,EAAA,CAAAC,cAAA,eAAoC;UAElCD,EAAA,CAAAoB,UAAA,KAAAmK,uCAAA,kBAGM;UAGNvL,EAAA,CAAAoB,UAAA,KAAAoK,uCAAA,kBAsCM;UAGNxL,EAAA,CAAAoB,UAAA,KAAAqK,uCAAA,mBAQM;UACRzL,EAAA,CAAAG,YAAA,EAAM;;;;;UA7KUH,EAAA,CAAAO,SAAA,GAA6B;UAA7BP,EAAA,CAAAI,UAAA,UAAAoK,GAAA,CAAAvG,iBAAA,CAA6B;UACDjE,EAAA,CAAAO,SAAA,GAAiB;UAAjBP,EAAA,CAAAI,UAAA,YAAAoK,GAAA,CAAArG,cAAA,CAAiB;UAsB3CnE,EAAA,CAAAO,SAAA,IAA4B;UAA5BP,EAAA,CAAAI,UAAA,UAAAoK,GAAA,CAAA7G,gBAAA,CAA4B;UAGlC3D,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAI,UAAA,gBAAAoK,GAAA,CAAA5G,kBAAA,CAAkC;UAKP5D,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,YAAAoK,GAAA,CAAA9G,gBAAA,CAAmB;UAexC1D,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAI,UAAA,gBAAAoK,GAAA,CAAAxG,YAAA,CAA4B;UACChE,EAAA,CAAAO,SAAA,GAAkB;UAAlBP,EAAA,CAAAI,UAAA,YAAAoK,GAAA,CAAApG,eAAA,CAAkB;UAe3CpE,EAAA,CAAAO,SAAA,GAA6B;UAA7BP,EAAA,CAAAI,UAAA,kBAAAsL,GAAA,CAA6B,gBAAAlB,GAAA,CAAA3G,SAAA;UACZ7D,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,QAAAsL,GAAA,CAAmB;UAapC1L,EAAA,CAAAO,SAAA,IAA2B;UAA3BP,EAAA,CAAAI,UAAA,kBAAAuL,GAAA,CAA2B,gBAAAnB,GAAA,CAAA1G,OAAA;UACV9D,EAAA,CAAAO,SAAA,GAAiB;UAAjBP,EAAA,CAAAI,UAAA,QAAAuL,GAAA,CAAiB;UA8B3C3L,EAAA,CAAAO,SAAA,IAA2B;UAA3BP,EAAA,CAAAI,UAAA,gBAAAoK,GAAA,CAAAzG,WAAA,CAA2B;UAUhC/D,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,SAAAoK,GAAA,CAAAtG,SAAA,CAAe;UAMflE,EAAA,CAAAO,SAAA,GAAkE;UAAlEP,EAAA,CAAAI,UAAA,UAAAoK,GAAA,CAAAtG,SAAA,KAAAsG,GAAA,CAAAjJ,YAAA,CAAAgB,MAAA,QAAAiI,GAAA,CAAArI,MAAA,CAAAI,MAAA,MAAkE;UAyClEvC,EAAA,CAAAO,SAAA,GAAoE;UAApEP,EAAA,CAAAI,UAAA,UAAAoK,GAAA,CAAAtG,SAAA,IAAAsG,GAAA,CAAAjJ,YAAA,CAAAgB,MAAA,UAAAiI,GAAA,CAAArI,MAAA,CAAAI,MAAA,OAAoE;;;qBDzI9E3D,YAAY,EAAAgN,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZlN,aAAa,EAAAmN,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACbtN,eAAe,EAAAuN,EAAA,CAAAC,SAAA,EACfvN,aAAa,EAAAwN,EAAA,CAAAC,OAAA,EACbxN,eAAe,EAAAyN,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,QAAA,EAAAF,GAAA,CAAAG,SAAA,EAAAC,GAAA,CAAAC,SAAA,EAAAC,GAAA,CAAAC,SAAA,EACf/N,kBAAkB,EAClBC,cAAc,EAAA+N,GAAA,CAAAC,QAAA,EACd/N,mBAAmB,EAAAgO,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,kBAAA,EAAAF,GAAA,CAAAG,mBAAA,EACnBlO,mBAAmB,EACnBC,gBAAgB,EAChBC,wBAAwB,EAAAiO,GAAA,CAAAC,kBAAA,EACxBjO,cAAc,EAAAkO,GAAA,CAAAC,kBAAA,EACdlO,wBAAwB,EAAAmO,GAAA,CAAAC,wBAAA,EACxBlO,mBAAmB,EAAAmO,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,eAAA,EAAAF,GAAA,CAAAG,oBAAA,EACnBrO,WAAW;MAAAsO,MAAA;IAAA;EAAA;;SAKFjL,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
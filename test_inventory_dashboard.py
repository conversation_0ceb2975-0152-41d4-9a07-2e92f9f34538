#!/usr/bin/env python3
"""
Test script for inventory dashboard functionality
"""

import sys
import os
import json
from datetime import datetime, timezone, timedelta

# Add the project root to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'digitoryjobsv4'))

try:
    from app.routers.report import store_variance
    from app.utility.dashboard_agents import generate_inventory_dashboard
    import pandas as pd
    
    print("✅ Successfully imported required modules")
    
    # Create a test job similar to what would come from the frontend
    test_job = {
        'tenantId': 'test_tenant',
        'details': {
            'selectedRestaurants': ['test_restaurant_1'],
            'selectedCategories': ['all'],
            'selectedSubCategories': ['all'],
            'selectedVendors': [],
            'selectedWorkAreas': [],
            'startDate': (datetime.now(timezone.utc) - timedelta(days=30)).isoformat(),
            'endDate': datetime.now(timezone.utc).isoformat(),
            'type': 'store_variance'
        }
    }
    
    sortby = {'name': 'Item Name', 'type': True}
    
    print("🔍 Testing store_variance function...")
    print(f"Job details: {json.dumps(test_job, indent=2, default=str)}")
    
    # Test store_variance function
    try:
        result = store_variance(test_job, sortby)
        print(f"✅ store_variance function executed successfully")
        print(f"Result status: {result.get('status')}")
        
        if result.get('status') == 'success' and 'response' in result:
            store_variance_data = result['response'].get('store_variance', [])
            print(f"📊 Retrieved {len(store_variance_data)} inventory records")
            
            if store_variance_data:
                # Convert to DataFrame
                df = pd.DataFrame(store_variance_data)
                print(f"📈 DataFrame created with shape: {df.shape}")
                print(f"📋 Columns: {list(df.columns)}")
                
                # Show sample data
                print("\n📋 Sample data (first 3 rows):")
                print(df.head(3).to_string())
                
                # Test dashboard generation
                print("\n🎯 Testing generate_inventory_dashboard...")
                dashboard_result = generate_inventory_dashboard(df)
                
                if dashboard_result.get('success'):
                    print("✅ Dashboard generation successful!")
                    print(f"📊 Generated {len(dashboard_result.get('charts', []))} charts")
                    print(f"📋 Generated {len(dashboard_result.get('summary_items', []))} summary items")
                    
                    # Show summary items
                    print("\n📋 Summary Items:")
                    for item in dashboard_result.get('summary_items', []):
                        print(f"  - {item['label']}: {item['value']} ({item['icon']})")
                    
                    # Show chart titles
                    print("\n📊 Charts:")
                    for chart in dashboard_result.get('charts', []):
                        print(f"  - {chart['title']} ({chart['type']})")
                        
                else:
                    print(f"❌ Dashboard generation failed: {dashboard_result.get('error')}")
            else:
                print("⚠️ No inventory data returned from store_variance")
        else:
            print(f"❌ store_variance failed: {result}")
            
    except Exception as e:
        print(f"❌ Error testing store_variance: {str(e)}")
        import traceback
        traceback.print_exc()
    
except ImportError as e:
    print(f"❌ Import error: {str(e)}")
    print("Make sure you're running this from the project root directory")
    print("And that all required dependencies are installed")
    
except Exception as e:
    print(f"❌ Unexpected error: {str(e)}")
    import traceback
    traceback.print_exc()

print("\n🏁 Test completed")

{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./dashboard-config.service\";\nclass ChartRendererService {\n  constructor(configService) {\n    this.configService = configService;\n  }\n  /**\n   * Process chart data from backend and prepare for ng2-charts\n   */\n  processChart(chartData) {\n    return {\n      id: chartData.id,\n      title: chartData.title,\n      type: chartData.type,\n      data: this.processChartData(chartData),\n      options: this.processChartOptions(chartData)\n    };\n  }\n  /**\n   * Process chart data structure\n   */\n  processChartData(chartData) {\n    // If backend already provides complete chart data, use it\n    if (chartData.data) {\n      return chartData.data;\n    }\n    // Fallback processing if needed\n    return {\n      labels: chartData.labels || [],\n      datasets: chartData.datasets || []\n    };\n  }\n  /**\n   * Process chart options - merge backend options with defaults\n   */\n  processChartOptions(chartData) {\n    // Get backend-provided options\n    const backendOptions = chartData.options || {};\n    // Merge with global defaults\n    const mergedOptions = this.configService.mergeChartOptions(backendOptions);\n    // Apply chart-type specific enhancements\n    return this.enhanceOptionsForChartType(mergedOptions, chartData.type, chartData.title);\n  }\n  /**\n   * Enhance options based on chart type\n   */\n  enhanceOptionsForChartType(options, chartType, title) {\n    const enhanced = {\n      ...options\n    };\n    switch (chartType) {\n      case 'doughnut':\n      case 'pie':\n        return this.enhancePieChartOptions(enhanced, title);\n      case 'bar':\n        return this.enhanceBarChartOptions(enhanced, title);\n      case 'horizontalBar':\n        return this.enhanceHorizontalBarChartOptions(enhanced, title);\n      case 'line':\n        return this.enhanceLineChartOptions(enhanced, title);\n      default:\n        return enhanced;\n    }\n  }\n  /**\n   * Enhance pie/doughnut chart options\n   */\n  enhancePieChartOptions(options, title) {\n    const currencySymbol = this.configService.getCurrencySymbol();\n    return {\n      ...options,\n      plugins: {\n        ...options.plugins,\n        legend: {\n          ...options.plugins?.legend,\n          position: 'right'\n        },\n        tooltip: {\n          ...options.plugins?.tooltip,\n          callbacks: {\n            label: context => {\n              const label = context.label || '';\n              const value = context.parsed;\n              const total = context.dataset.data.reduce((a, b) => a + b, 0);\n              const percentage = (value / total * 100).toFixed(1);\n              if (this.isCurrencyChart(title)) {\n                return `${label}: ${currencySymbol}${value.toLocaleString()} (${percentage}%)`;\n              } else {\n                return `${label}: ${value.toLocaleString()} (${percentage}%)`;\n              }\n            }\n          }\n        }\n      }\n    };\n  }\n  /**\n   * Enhance bar chart options\n   */\n  enhanceBarChartOptions(options, title) {\n    return {\n      ...options,\n      plugins: {\n        ...options.plugins,\n        tooltip: {\n          ...options.plugins?.tooltip,\n          callbacks: {\n            label: context => {\n              const label = context.dataset.label || '';\n              const value = context.parsed.y;\n              if (this.isCurrencyChart(title)) {\n                return `${label}: ${this.configService.formatLargeNumber(value)}`;\n              } else if (this.isPercentageChart(title)) {\n                return `${label}: ${value.toFixed(1)}%`;\n              } else {\n                return `${label}: ${value.toLocaleString()}`;\n              }\n            }\n          }\n        }\n      },\n      scales: {\n        ...options.scales,\n        y: {\n          ...options.scales?.y,\n          ticks: {\n            ...options.scales?.y?.ticks,\n            callback: value => {\n              if (this.isCurrencyChart(title)) {\n                return this.configService.formatLargeNumber(value);\n              } else if (this.isPercentageChart(title)) {\n                return `${value}%`;\n              } else {\n                return value.toLocaleString();\n              }\n            }\n          }\n        }\n      }\n    };\n  }\n  /**\n   * Enhance horizontal bar chart options\n   */\n  enhanceHorizontalBarChartOptions(options, title) {\n    const enhanced = this.enhanceBarChartOptions(options, title);\n    return {\n      ...enhanced,\n      indexAxis: 'y',\n      plugins: {\n        ...enhanced.plugins,\n        legend: {\n          ...enhanced.plugins?.legend,\n          display: false // Usually hide legend for horizontal bars\n        }\n      },\n\n      scales: {\n        x: enhanced.scales?.['y'],\n        y: {\n          ...enhanced.scales?.['x'],\n          grid: {\n            display: false\n          }\n        }\n      }\n    };\n  }\n  /**\n   * Enhance line chart options\n   */\n  enhanceLineChartOptions(options, _title) {\n    return {\n      ...options,\n      interaction: {\n        mode: 'index',\n        intersect: false\n      },\n      plugins: {\n        ...options.plugins,\n        tooltip: {\n          ...options.plugins?.tooltip,\n          mode: 'index',\n          intersect: false\n        }\n      },\n      scales: {\n        ...options.scales,\n        x: {\n          ...options.scales?.x,\n          grid: {\n            display: true,\n            color: 'rgba(0,0,0,0.1)'\n          }\n        },\n        y: {\n          ...options.scales?.y,\n          grid: {\n            display: true,\n            color: 'rgba(0,0,0,0.1)'\n          }\n        }\n      }\n    };\n  }\n  /**\n   * Check if chart deals with currency values\n   */\n  isCurrencyChart(title) {\n    const currencyKeywords = ['value', 'amount', 'cost', 'price', 'revenue', 'profit', 'loss', '₹'];\n    return currencyKeywords.some(keyword => title.toLowerCase().includes(keyword.toLowerCase()));\n  }\n  /**\n   * Check if chart deals with percentage values\n   */\n  isPercentageChart(title) {\n    const percentageKeywords = ['percentage', '%', 'rate', 'ratio', 'growth'];\n    return percentageKeywords.some(keyword => title.toLowerCase().includes(keyword.toLowerCase()));\n  }\n  /**\n   * Get chart type for ng2-charts\n   */\n  getChartType(type) {\n    const typeMap = {\n      'bar': 'bar',\n      'horizontalBar': 'bar',\n      'line': 'line',\n      'doughnut': 'doughnut',\n      'pie': 'pie',\n      'radar': 'radar',\n      'polarArea': 'polarArea'\n    };\n    return typeMap[type] || 'bar';\n  }\n  /**\n   * Get chart CSS class based on type and content\n   */\n  getChartCssClass(chart) {\n    const classes = ['chart-container'];\n    // Add type-specific classes\n    classes.push(`chart-${chart.type}`);\n    // Add size classes based on chart type\n    if (chart.type === 'line' || chart.title.toLowerCase().includes('trend')) {\n      classes.push('full-width');\n    } else if (chart.type === 'doughnut' || chart.type === 'pie') {\n      classes.push('third-width');\n    } else {\n      classes.push('half-width');\n    }\n    return classes.join(' ');\n  }\n  /**\n   * Process multiple charts from backend response\n   */\n  processCharts(chartsData) {\n    return chartsData.map(chartData => this.processChart(chartData));\n  }\n  static {\n    this.ɵfac = function ChartRendererService_Factory(t) {\n      return new (t || ChartRendererService)(i0.ɵɵinject(i1.DashboardConfigService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ChartRendererService,\n      factory: ChartRendererService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\nexport { ChartRendererService };", "map": {"version": 3, "names": ["ChartRendererService", "constructor", "configService", "processChart", "chartData", "id", "title", "type", "data", "processChartData", "options", "processChartOptions", "labels", "datasets", "backendOptions", "mergedOptions", "mergeChartOptions", "enhanceOptionsForChartType", "chartType", "enhanced", "enhancePieChartOptions", "enhanceBarChartOptions", "enhanceHorizontalBarChartOptions", "enhanceLineChartOptions", "currencySymbol", "getCurrencySymbol", "plugins", "legend", "position", "tooltip", "callbacks", "label", "context", "value", "parsed", "total", "dataset", "reduce", "a", "b", "percentage", "toFixed", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toLocaleString", "y", "formatLargeNumber", "isPercentageChart", "scales", "ticks", "callback", "indexAxis", "display", "x", "grid", "_title", "interaction", "mode", "intersect", "color", "currencyKeywords", "some", "keyword", "toLowerCase", "includes", "percentageKeywords", "getChartType", "typeMap", "getChartCssClass", "chart", "classes", "push", "join", "process<PERSON><PERSON><PERSON>", "chartsData", "map", "i0", "ɵɵinject", "i1", "DashboardConfigService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\digii\\digitorywebv4\\src\\app\\services\\chart-renderer.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { ChartConfiguration, ChartData, ChartType } from 'chart.js';\nimport { DashboardConfigService } from './dashboard-config.service';\n\nexport interface ChartModel {\n  id: string;\n  title: string;\n  type: string;\n  data: ChartData;\n  options?: any;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ChartRendererService {\n\n  constructor(private configService: DashboardConfigService) {}\n\n  /**\n   * Process chart data from backend and prepare for ng2-charts\n   */\n  processChart(chartData: any): ChartModel {\n    return {\n      id: chartData.id,\n      title: chartData.title,\n      type: chartData.type,\n      data: this.processChartData(chartData),\n      options: this.processChartOptions(chartData)\n    };\n  }\n\n  /**\n   * Process chart data structure\n   */\n  private processChartData(chartData: any): ChartData {\n    // If backend already provides complete chart data, use it\n    if (chartData.data) {\n      return chartData.data;\n    }\n\n    // Fallback processing if needed\n    return {\n      labels: chartData.labels || [],\n      datasets: chartData.datasets || []\n    };\n  }\n\n  /**\n   * Process chart options - merge backend options with defaults\n   */\n  private processChartOptions(chartData: any): ChartConfiguration['options'] {\n    // Get backend-provided options\n    const backendOptions = chartData.options || {};\n    \n    // Merge with global defaults\n    const mergedOptions = this.configService.mergeChartOptions(backendOptions);\n    \n    // Apply chart-type specific enhancements\n    return this.enhanceOptionsForChartType(mergedOptions, chartData.type, chartData.title);\n  }\n\n  /**\n   * Enhance options based on chart type\n   */\n  private enhanceOptionsForChartType(options: any, chartType: string, title: string): ChartConfiguration['options'] {\n    const enhanced = { ...options };\n\n    switch (chartType) {\n      case 'doughnut':\n      case 'pie':\n        return this.enhancePieChartOptions(enhanced, title);\n      \n      case 'bar':\n        return this.enhanceBarChartOptions(enhanced, title);\n      \n      case 'horizontalBar':\n        return this.enhanceHorizontalBarChartOptions(enhanced, title);\n      \n      case 'line':\n        return this.enhanceLineChartOptions(enhanced, title);\n      \n      default:\n        return enhanced;\n    }\n  }\n\n  /**\n   * Enhance pie/doughnut chart options\n   */\n  private enhancePieChartOptions(options: any, title: string): ChartConfiguration['options'] {\n    const currencySymbol = this.configService.getCurrencySymbol();\n    \n    return {\n      ...options,\n      plugins: {\n        ...options.plugins,\n        legend: {\n          ...options.plugins?.legend,\n          position: 'right'\n        },\n        tooltip: {\n          ...options.plugins?.tooltip,\n          callbacks: {\n            label: (context: any) => {\n              const label = context.label || '';\n              const value = context.parsed;\n              const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);\n              const percentage = ((value / total) * 100).toFixed(1);\n              \n              if (this.isCurrencyChart(title)) {\n                return `${label}: ${currencySymbol}${value.toLocaleString()} (${percentage}%)`;\n              } else {\n                return `${label}: ${value.toLocaleString()} (${percentage}%)`;\n              }\n            }\n          }\n        }\n      }\n    };\n  }\n\n  /**\n   * Enhance bar chart options\n   */\n  private enhanceBarChartOptions(options: any, title: string): ChartConfiguration['options'] {\n    return {\n      ...options,\n      plugins: {\n        ...options.plugins,\n        tooltip: {\n          ...options.plugins?.tooltip,\n          callbacks: {\n            label: (context: any) => {\n              const label = context.dataset.label || '';\n              const value = context.parsed.y;\n              \n              if (this.isCurrencyChart(title)) {\n                return `${label}: ${this.configService.formatLargeNumber(value)}`;\n              } else if (this.isPercentageChart(title)) {\n                return `${label}: ${value.toFixed(1)}%`;\n              } else {\n                return `${label}: ${value.toLocaleString()}`;\n              }\n            }\n          }\n        }\n      },\n      scales: {\n        ...options.scales,\n        y: {\n          ...options.scales?.y,\n          ticks: {\n            ...options.scales?.y?.ticks,\n            callback: (value: any) => {\n              if (this.isCurrencyChart(title)) {\n                return this.configService.formatLargeNumber(value);\n              } else if (this.isPercentageChart(title)) {\n                return `${value}%`;\n              } else {\n                return value.toLocaleString();\n              }\n            }\n          }\n        }\n      }\n    };\n  }\n\n  /**\n   * Enhance horizontal bar chart options\n   */\n  private enhanceHorizontalBarChartOptions(options: any, title: string): ChartConfiguration['options'] {\n    const enhanced = this.enhanceBarChartOptions(options, title);\n    \n    return {\n      ...enhanced,\n      indexAxis: 'y' as const,\n      plugins: {\n        ...enhanced.plugins,\n        legend: {\n          ...enhanced.plugins?.legend,\n          display: false // Usually hide legend for horizontal bars\n        }\n      },\n      scales: {\n        x: enhanced.scales?.['y'], // Swap x and y for horizontal\n        y: {\n          ...enhanced.scales?.['x'],\n          grid: { display: false }\n        }\n      }\n    };\n  }\n\n  /**\n   * Enhance line chart options\n   */\n  private enhanceLineChartOptions(options: any, _title: string): ChartConfiguration['options'] {\n    return {\n      ...options,\n      interaction: {\n        mode: 'index',\n        intersect: false\n      },\n      plugins: {\n        ...options.plugins,\n        tooltip: {\n          ...options.plugins?.tooltip,\n          mode: 'index',\n          intersect: false\n        }\n      },\n      scales: {\n        ...options.scales,\n        x: {\n          ...options.scales?.x,\n          grid: { display: true, color: 'rgba(0,0,0,0.1)' }\n        },\n        y: {\n          ...options.scales?.y,\n          grid: { display: true, color: 'rgba(0,0,0,0.1)' }\n        }\n      }\n    };\n  }\n\n  /**\n   * Check if chart deals with currency values\n   */\n  private isCurrencyChart(title: string): boolean {\n    const currencyKeywords = ['value', 'amount', 'cost', 'price', 'revenue', 'profit', 'loss', '₹'];\n    return currencyKeywords.some(keyword => \n      title.toLowerCase().includes(keyword.toLowerCase())\n    );\n  }\n\n  /**\n   * Check if chart deals with percentage values\n   */\n  private isPercentageChart(title: string): boolean {\n    const percentageKeywords = ['percentage', '%', 'rate', 'ratio', 'growth'];\n    return percentageKeywords.some(keyword => \n      title.toLowerCase().includes(keyword.toLowerCase())\n    );\n  }\n\n  /**\n   * Get chart type for ng2-charts\n   */\n  getChartType(type: string): ChartType {\n    const typeMap: { [key: string]: ChartType } = {\n      'bar': 'bar',\n      'horizontalBar': 'bar',\n      'line': 'line',\n      'doughnut': 'doughnut',\n      'pie': 'pie',\n      'radar': 'radar',\n      'polarArea': 'polarArea'\n    };\n    \n    return typeMap[type] || 'bar';\n  }\n\n  /**\n   * Get chart CSS class based on type and content\n   */\n  getChartCssClass(chart: ChartModel): string {\n    const classes = ['chart-container'];\n    \n    // Add type-specific classes\n    classes.push(`chart-${chart.type}`);\n    \n    // Add size classes based on chart type\n    if (chart.type === 'line' || chart.title.toLowerCase().includes('trend')) {\n      classes.push('full-width');\n    } else if (chart.type === 'doughnut' || chart.type === 'pie') {\n      classes.push('third-width');\n    } else {\n      classes.push('half-width');\n    }\n    \n    return classes.join(' ');\n  }\n\n  /**\n   * Process multiple charts from backend response\n   */\n  processCharts(chartsData: any[]): ChartModel[] {\n    return chartsData.map(chartData => this.processChart(chartData));\n  }\n}\n"], "mappings": ";;AAYA,MAGaA,oBAAoB;EAE/BC,YAAoBC,aAAqC;IAArC,KAAAA,aAAa,GAAbA,aAAa;EAA2B;EAE5D;;;EAGAC,YAAYA,CAACC,SAAc;IACzB,OAAO;MACLC,EAAE,EAAED,SAAS,CAACC,EAAE;MAChBC,KAAK,EAAEF,SAAS,CAACE,KAAK;MACtBC,IAAI,EAAEH,SAAS,CAACG,IAAI;MACpBC,IAAI,EAAE,IAAI,CAACC,gBAAgB,CAACL,SAAS,CAAC;MACtCM,OAAO,EAAE,IAAI,CAACC,mBAAmB,CAACP,SAAS;KAC5C;EACH;EAEA;;;EAGQK,gBAAgBA,CAACL,SAAc;IACrC;IACA,IAAIA,SAAS,CAACI,IAAI,EAAE;MAClB,OAAOJ,SAAS,CAACI,IAAI;;IAGvB;IACA,OAAO;MACLI,MAAM,EAAER,SAAS,CAACQ,MAAM,IAAI,EAAE;MAC9BC,QAAQ,EAAET,SAAS,CAACS,QAAQ,IAAI;KACjC;EACH;EAEA;;;EAGQF,mBAAmBA,CAACP,SAAc;IACxC;IACA,MAAMU,cAAc,GAAGV,SAAS,CAACM,OAAO,IAAI,EAAE;IAE9C;IACA,MAAMK,aAAa,GAAG,IAAI,CAACb,aAAa,CAACc,iBAAiB,CAACF,cAAc,CAAC;IAE1E;IACA,OAAO,IAAI,CAACG,0BAA0B,CAACF,aAAa,EAAEX,SAAS,CAACG,IAAI,EAAEH,SAAS,CAACE,KAAK,CAAC;EACxF;EAEA;;;EAGQW,0BAA0BA,CAACP,OAAY,EAAEQ,SAAiB,EAAEZ,KAAa;IAC/E,MAAMa,QAAQ,GAAG;MAAE,GAAGT;IAAO,CAAE;IAE/B,QAAQQ,SAAS;MACf,KAAK,UAAU;MACf,KAAK,KAAK;QACR,OAAO,IAAI,CAACE,sBAAsB,CAACD,QAAQ,EAAEb,KAAK,CAAC;MAErD,KAAK,KAAK;QACR,OAAO,IAAI,CAACe,sBAAsB,CAACF,QAAQ,EAAEb,KAAK,CAAC;MAErD,KAAK,eAAe;QAClB,OAAO,IAAI,CAACgB,gCAAgC,CAACH,QAAQ,EAAEb,KAAK,CAAC;MAE/D,KAAK,MAAM;QACT,OAAO,IAAI,CAACiB,uBAAuB,CAACJ,QAAQ,EAAEb,KAAK,CAAC;MAEtD;QACE,OAAOa,QAAQ;;EAErB;EAEA;;;EAGQC,sBAAsBA,CAACV,OAAY,EAAEJ,KAAa;IACxD,MAAMkB,cAAc,GAAG,IAAI,CAACtB,aAAa,CAACuB,iBAAiB,EAAE;IAE7D,OAAO;MACL,GAAGf,OAAO;MACVgB,OAAO,EAAE;QACP,GAAGhB,OAAO,CAACgB,OAAO;QAClBC,MAAM,EAAE;UACN,GAAGjB,OAAO,CAACgB,OAAO,EAAEC,MAAM;UAC1BC,QAAQ,EAAE;SACX;QACDC,OAAO,EAAE;UACP,GAAGnB,OAAO,CAACgB,OAAO,EAAEG,OAAO;UAC3BC,SAAS,EAAE;YACTC,KAAK,EAAGC,OAAY,IAAI;cACtB,MAAMD,KAAK,GAAGC,OAAO,CAACD,KAAK,IAAI,EAAE;cACjC,MAAME,KAAK,GAAGD,OAAO,CAACE,MAAM;cAC5B,MAAMC,KAAK,GAAGH,OAAO,CAACI,OAAO,CAAC5B,IAAI,CAAC6B,MAAM,CAAC,CAACC,CAAS,EAAEC,CAAS,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC;cAC7E,MAAMC,UAAU,GAAG,CAAEP,KAAK,GAAGE,KAAK,GAAI,GAAG,EAAEM,OAAO,CAAC,CAAC,CAAC;cAErD,IAAI,IAAI,CAACC,eAAe,CAACpC,KAAK,CAAC,EAAE;gBAC/B,OAAO,GAAGyB,KAAK,KAAKP,cAAc,GAAGS,KAAK,CAACU,cAAc,EAAE,KAAKH,UAAU,IAAI;eAC/E,MAAM;gBACL,OAAO,GAAGT,KAAK,KAAKE,KAAK,CAACU,cAAc,EAAE,KAAKH,UAAU,IAAI;;YAEjE;;;;KAIP;EACH;EAEA;;;EAGQnB,sBAAsBA,CAACX,OAAY,EAAEJ,KAAa;IACxD,OAAO;MACL,GAAGI,OAAO;MACVgB,OAAO,EAAE;QACP,GAAGhB,OAAO,CAACgB,OAAO;QAClBG,OAAO,EAAE;UACP,GAAGnB,OAAO,CAACgB,OAAO,EAAEG,OAAO;UAC3BC,SAAS,EAAE;YACTC,KAAK,EAAGC,OAAY,IAAI;cACtB,MAAMD,KAAK,GAAGC,OAAO,CAACI,OAAO,CAACL,KAAK,IAAI,EAAE;cACzC,MAAME,KAAK,GAAGD,OAAO,CAACE,MAAM,CAACU,CAAC;cAE9B,IAAI,IAAI,CAACF,eAAe,CAACpC,KAAK,CAAC,EAAE;gBAC/B,OAAO,GAAGyB,KAAK,KAAK,IAAI,CAAC7B,aAAa,CAAC2C,iBAAiB,CAACZ,KAAK,CAAC,EAAE;eAClE,MAAM,IAAI,IAAI,CAACa,iBAAiB,CAACxC,KAAK,CAAC,EAAE;gBACxC,OAAO,GAAGyB,KAAK,KAAKE,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC,GAAG;eACxC,MAAM;gBACL,OAAO,GAAGV,KAAK,KAAKE,KAAK,CAACU,cAAc,EAAE,EAAE;;YAEhD;;;OAGL;MACDI,MAAM,EAAE;QACN,GAAGrC,OAAO,CAACqC,MAAM;QACjBH,CAAC,EAAE;UACD,GAAGlC,OAAO,CAACqC,MAAM,EAAEH,CAAC;UACpBI,KAAK,EAAE;YACL,GAAGtC,OAAO,CAACqC,MAAM,EAAEH,CAAC,EAAEI,KAAK;YAC3BC,QAAQ,EAAGhB,KAAU,IAAI;cACvB,IAAI,IAAI,CAACS,eAAe,CAACpC,KAAK,CAAC,EAAE;gBAC/B,OAAO,IAAI,CAACJ,aAAa,CAAC2C,iBAAiB,CAACZ,KAAK,CAAC;eACnD,MAAM,IAAI,IAAI,CAACa,iBAAiB,CAACxC,KAAK,CAAC,EAAE;gBACxC,OAAO,GAAG2B,KAAK,GAAG;eACnB,MAAM;gBACL,OAAOA,KAAK,CAACU,cAAc,EAAE;;YAEjC;;;;KAIP;EACH;EAEA;;;EAGQrB,gCAAgCA,CAACZ,OAAY,EAAEJ,KAAa;IAClE,MAAMa,QAAQ,GAAG,IAAI,CAACE,sBAAsB,CAACX,OAAO,EAAEJ,KAAK,CAAC;IAE5D,OAAO;MACL,GAAGa,QAAQ;MACX+B,SAAS,EAAE,GAAY;MACvBxB,OAAO,EAAE;QACP,GAAGP,QAAQ,CAACO,OAAO;QACnBC,MAAM,EAAE;UACN,GAAGR,QAAQ,CAACO,OAAO,EAAEC,MAAM;UAC3BwB,OAAO,EAAE,KAAK,CAAC;;OAElB;;MACDJ,MAAM,EAAE;QACNK,CAAC,EAAEjC,QAAQ,CAAC4B,MAAM,GAAG,GAAG,CAAC;QACzBH,CAAC,EAAE;UACD,GAAGzB,QAAQ,CAAC4B,MAAM,GAAG,GAAG,CAAC;UACzBM,IAAI,EAAE;YAAEF,OAAO,EAAE;UAAK;;;KAG3B;EACH;EAEA;;;EAGQ5B,uBAAuBA,CAACb,OAAY,EAAE4C,MAAc;IAC1D,OAAO;MACL,GAAG5C,OAAO;MACV6C,WAAW,EAAE;QACXC,IAAI,EAAE,OAAO;QACbC,SAAS,EAAE;OACZ;MACD/B,OAAO,EAAE;QACP,GAAGhB,OAAO,CAACgB,OAAO;QAClBG,OAAO,EAAE;UACP,GAAGnB,OAAO,CAACgB,OAAO,EAAEG,OAAO;UAC3B2B,IAAI,EAAE,OAAO;UACbC,SAAS,EAAE;;OAEd;MACDV,MAAM,EAAE;QACN,GAAGrC,OAAO,CAACqC,MAAM;QACjBK,CAAC,EAAE;UACD,GAAG1C,OAAO,CAACqC,MAAM,EAAEK,CAAC;UACpBC,IAAI,EAAE;YAAEF,OAAO,EAAE,IAAI;YAAEO,KAAK,EAAE;UAAiB;SAChD;QACDd,CAAC,EAAE;UACD,GAAGlC,OAAO,CAACqC,MAAM,EAAEH,CAAC;UACpBS,IAAI,EAAE;YAAEF,OAAO,EAAE,IAAI;YAAEO,KAAK,EAAE;UAAiB;;;KAGpD;EACH;EAEA;;;EAGQhB,eAAeA,CAACpC,KAAa;IACnC,MAAMqD,gBAAgB,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,CAAC;IAC/F,OAAOA,gBAAgB,CAACC,IAAI,CAACC,OAAO,IAClCvD,KAAK,CAACwD,WAAW,EAAE,CAACC,QAAQ,CAACF,OAAO,CAACC,WAAW,EAAE,CAAC,CACpD;EACH;EAEA;;;EAGQhB,iBAAiBA,CAACxC,KAAa;IACrC,MAAM0D,kBAAkB,GAAG,CAAC,YAAY,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC;IACzE,OAAOA,kBAAkB,CAACJ,IAAI,CAACC,OAAO,IACpCvD,KAAK,CAACwD,WAAW,EAAE,CAACC,QAAQ,CAACF,OAAO,CAACC,WAAW,EAAE,CAAC,CACpD;EACH;EAEA;;;EAGAG,YAAYA,CAAC1D,IAAY;IACvB,MAAM2D,OAAO,GAAiC;MAC5C,KAAK,EAAE,KAAK;MACZ,eAAe,EAAE,KAAK;MACtB,MAAM,EAAE,MAAM;MACd,UAAU,EAAE,UAAU;MACtB,KAAK,EAAE,KAAK;MACZ,OAAO,EAAE,OAAO;MAChB,WAAW,EAAE;KACd;IAED,OAAOA,OAAO,CAAC3D,IAAI,CAAC,IAAI,KAAK;EAC/B;EAEA;;;EAGA4D,gBAAgBA,CAACC,KAAiB;IAChC,MAAMC,OAAO,GAAG,CAAC,iBAAiB,CAAC;IAEnC;IACAA,OAAO,CAACC,IAAI,CAAC,SAASF,KAAK,CAAC7D,IAAI,EAAE,CAAC;IAEnC;IACA,IAAI6D,KAAK,CAAC7D,IAAI,KAAK,MAAM,IAAI6D,KAAK,CAAC9D,KAAK,CAACwD,WAAW,EAAE,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MACxEM,OAAO,CAACC,IAAI,CAAC,YAAY,CAAC;KAC3B,MAAM,IAAIF,KAAK,CAAC7D,IAAI,KAAK,UAAU,IAAI6D,KAAK,CAAC7D,IAAI,KAAK,KAAK,EAAE;MAC5D8D,OAAO,CAACC,IAAI,CAAC,aAAa,CAAC;KAC5B,MAAM;MACLD,OAAO,CAACC,IAAI,CAAC,YAAY,CAAC;;IAG5B,OAAOD,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC;EAC1B;EAEA;;;EAGAC,aAAaA,CAACC,UAAiB;IAC7B,OAAOA,UAAU,CAACC,GAAG,CAACtE,SAAS,IAAI,IAAI,CAACD,YAAY,CAACC,SAAS,CAAC,CAAC;EAClE;;;uBAnRWJ,oBAAoB,EAAA2E,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,sBAAA;IAAA;EAAA;;;aAApB9E,oBAAoB;MAAA+E,OAAA,EAApB/E,oBAAoB,CAAAgF,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA;;SAEPjF,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
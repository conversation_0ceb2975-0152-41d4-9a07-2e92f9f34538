import { <PERSON>mpo<PERSON>, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { NgChartsModule } from 'ng2-charts';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { FormControl, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';
import { ChartConfiguration, ChartData, ChartType } from 'chart.js';

import { SmartDashboardService } from '../../services/smart-dashboard.service';
import { AuthService } from '../../services/auth.service';
import { ShareDataService } from '../../services/share-data.service';
import { DashboardConfigService } from '../../services/dashboard-config.service';
import { ChartRendererService, ChartModel } from '../../services/chart-renderer.service';

interface SummaryCard {
  icon: string;
  value: string;
  label: string;
  color: string;
  data_type?: string;
}

@Component({
  selector: 'app-smart-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    NgChartsModule,
    NgxMatSelectSearchModule,
    ReactiveFormsModule,
    FormsModule
  ],
  templateUrl: './smart-dashboard.component.html',
  styleUrls: ['./smart-dashboard.component.scss']
})
export class SmartDashboardComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  // User and branch data
  user: any;
  branches: any[] = [];
  filteredBranches: any[] = [];
  selectedLocation: string | null = null;
  
  // Form controls
  locationFilterCtrl = new FormControl('');
  startDate = new FormControl(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)); // 30 days ago
  endDate = new FormControl(new Date());
  searchQuery = new FormControl('');
  baseDateCtrl = new FormControl('deliveryDate');
  selectedDashboard = 'purchase';
  
  // Dashboard data
  summaryCards: SummaryCard[] = [];
  charts: ChartModel[] = [];
  isLoading = false;





  constructor(
    private smartDashboardService: SmartDashboardService,
    private authService: AuthService,
    private shareDataService: ShareDataService,
    private configService: DashboardConfigService,
    private chartRenderer: ChartRendererService,
    private cdr: ChangeDetectorRef
  ) {
    this.user = this.authService.getCurrentUser();
    this.initializeConfig();
  }

  private initializeConfig(): void {
    // Load dashboard configuration on component initialization
    this.configService.loadConfig().subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.configService.setConfig(response.data);
        }
      },
      error: (error) => {
        console.error('Failed to load dashboard config:', error);
      }
    });
  }

  ngOnInit(): void {
    this.initializeFilters();
    this.loadBranches();
    this.loadDashboardData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeFilters(): void {
    // Location filter
    this.locationFilterCtrl.valueChanges
      .pipe(
        debounceTime(200),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((value: string | null) => {
        this.filterBranches(value || '');
      });
  }

  private loadBranches(): void {
    this.shareDataService.selectedBranchesSource
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.branches = data || [];
        this.filteredBranches = [...this.branches];
        
        if (this.branches.length === 1) {
          this.selectedLocation = this.branches[0].restaurantIdOld;
        }
      });
  }

  private filterBranches(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredBranches = [...this.branches];
    } else {
      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\s/g, '');
      this.filteredBranches = this.branches.filter(branch =>
        branch.branchName.toLowerCase().replace(/\s/g, '').includes(normalizedSearchTerm)
      );
    }
  }

  loadDashboardData(): void {
    if (!this.startDate.value || !this.endDate.value) {
      return;
    }

    this.isLoading = true;

    const filters = {
      locations: this.selectedLocation ? [this.selectedLocation] : [],
      startDate: this.formatDate(this.startDate.value),
      endDate: this.formatDate(this.endDate.value),
      baseDate: this.baseDateCtrl.value || 'deliveryDate'
    };

    const request = {
      tenant_id: this.user.tenantId,
      filters: filters,
      user_query: '',
      use_default_charts: true,
      dashboard_type: this.selectedDashboard
    };

    this.smartDashboardService.getSmartDashboardData(request)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.processDashboardData(response.data);
          } else {
            console.warn('API returned no data');
            this.clearDashboardData();
          }
          this.isLoading = false;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error loading dashboard data:', error);
          this.clearDashboardData();
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  private clearDashboardData(): void {
    this.summaryCards = [];
    this.charts = [];
  }

  private processDashboardData(data: any): void {
    // Process summary cards using config service
    this.summaryCards = data.summary_items?.map((item: any) => ({
      icon: item.icon || this.configService.getSummaryCardIcon(item.data_type),
      value: item.value,
      label: item.label,
      color: this.configService.getSummaryCardColor(item.data_type),
      data_type: item.data_type
    })) || [];

    // Process charts using chart renderer service
    this.charts = data.charts?.map((chart: any) =>
      this.chartRenderer.processChart(chart)
    ) || [];

    console.log('Dashboard data processed:', {
      summaryCards: this.summaryCards.length,
      charts: this.charts.length
    });
  }





  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  onLocationChange(): void {
    this.loadDashboardData();
  }

  onDateChange(): void {
    this.loadDashboardData();
  }

  onDashboardChange(): void {
    this.loadDashboardData();
  }

  onSearchQuery(): void {
    const query = this.searchQuery.value?.trim();
    if (query) {
      this.loadDashboardDataWithQuery(query);
    } else {
      this.loadDashboardData();
    }
  }

  private loadDashboardDataWithQuery(query: string): void {
    if (!this.startDate.value || !this.endDate.value) {
      return;
    }

    this.isLoading = true;

    const filters = {
      locations: this.selectedLocation ? [this.selectedLocation] : [],
      startDate: this.formatDate(this.startDate.value),
      endDate: this.formatDate(this.endDate.value),
      baseDate: this.baseDateCtrl.value || 'deliveryDate'
    };

    const request = {
      tenant_id: this.user.tenantId,
      filters: filters,
      user_query: query,
      use_default_charts: false,
      dashboard_type: this.selectedDashboard
    };

    this.smartDashboardService.getSmartDashboardData(request)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.processDashboardData(response.data);
          } else {
            console.warn('Query returned no data');
            this.clearDashboardData();
          }
          this.isLoading = false;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error with query:', error);
          this.clearDashboardData();
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  // Dynamic chart methods using services
  getChartData(chart: ChartModel): ChartData {
    return chart.data;
  }

  getChartType(chart: ChartModel): ChartType {
    return this.chartRenderer.getChartType(chart.type);
  }

  getChartOptions(chart: ChartModel): ChartConfiguration['options'] {
    return chart.options || this.configService.getDefaultChartOptions();
  }

  getChartCssClass(chart: ChartModel): string {
    return this.chartRenderer.getChartCssClass(chart);
  }


}

import { Compo<PERSON>, OnIni<PERSON>, On<PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatExpansionModule } from '@angular/material/expansion';
import { NgChartsModule } from 'ng2-charts';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { FormControl, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';
import { ChartConfiguration, ChartData, ChartType } from 'chart.js';

import { SmartDashboardService, CardGroup, CardGroupsData } from '../../services/smart-dashboard.service';
import { AuthService } from '../../services/auth.service';
import { ShareDataService } from '../../services/share-data.service';
import { DashboardConfigService, DashboardType, BaseDateOption } from '../../services/dashboard-config.service';
import { ChartRendererService, ChartModel } from '../../services/chart-renderer.service';

interface SummaryCard {
  icon: string;
  value: string;
  label: string;
  color: string;
  data_type?: string;
}

@Component({
  selector: 'app-smart-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatExpansionModule,
    NgChartsModule,
    NgxMatSelectSearchModule,
    ReactiveFormsModule,
    FormsModule
  ],
  templateUrl: './smart-dashboard.component.html',
  styleUrls: ['./smart-dashboard.component.scss']
})
export class SmartDashboardComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  // User and branch data
  user: any;
  branches: any[] = [];
  filteredBranches: any[] = [];
  selectedLocation: string | null = null;
  
  // Form controls
  locationFilterCtrl = new FormControl('');
  startDate = new FormControl();
  endDate = new FormControl();
  searchQuery = new FormControl('');
  baseDateCtrl = new FormControl();
  selectedDashboard = '';

  // Dashboard data
  summaryCards: SummaryCard[] = [];
  charts: ChartModel[] = [];
  isLoading = false;
  isConfigLoaded = false;

  // Grouped cards data
  cardGroups: CardGroup[] = [];
  cardGroupsData: CardGroupsData | null = null;
  useGroupedCards = false;

  // Dynamic configuration data
  dashboardTypes: DashboardType[] = [];
  baseDateOptions: BaseDateOption[] = [];





  constructor(
    private smartDashboardService: SmartDashboardService,
    private authService: AuthService,
    private shareDataService: ShareDataService,
    private configService: DashboardConfigService,
    private chartRenderer: ChartRendererService,
    private cdr: ChangeDetectorRef
  ) {
    this.user = this.authService.getCurrentUser();
    this.initializeConfig();
  }

  private initializeConfig(): void {
    // Load dashboard configuration on component initialization
    this.configService.loadConfig().subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.configService.setConfig(response.data);
          this.setupDynamicConfigurations(response.data);
        } else {
          this.setupDefaultConfigurations();
        }
        this.isConfigLoaded = true;
        this.cdr.detectChanges();
      },
      error: () => {
        this.setupDefaultConfigurations();
        this.isConfigLoaded = true;
        this.cdr.detectChanges();
      }
    });
  }

  private setupDynamicConfigurations(config: any): void {
    // Set dashboard types
    this.dashboardTypes = config.dashboard_types || [];

    // Set base date options
    this.baseDateOptions = config.base_date_options || [];

    // Set default values from UI config
    const uiConfig = config.ui_config || {};
    const defaultDateRangeDays = uiConfig.default_date_range_days || 30;

    // Set default form values
    this.selectedDashboard = uiConfig.default_dashboard_type || 'purchase';
    this.baseDateCtrl.setValue(uiConfig.default_base_date || 'deliveryDate');
    this.startDate.setValue(new Date(Date.now() - defaultDateRangeDays * 24 * 60 * 60 * 1000));
    this.endDate.setValue(new Date());

    // Load dashboard data after configuration is set
    setTimeout(() => {
      this.loadDashboardData();
    }, 100);
  }

  private setupDefaultConfigurations(): void {
    // Fallback configurations if backend fails
    this.dashboardTypes = [
      { value: 'purchase', label: 'Purchase Dashboard' },
      { value: 'inventory', label: 'Inventory Dashboard' },
      { value: 'sales', label: 'Sales Dashboard' }
    ];
    this.baseDateOptions = [
      { value: 'deliveryDate', label: 'Delivery Date' },
      { value: 'orderDate', label: 'Order Date' },
      { value: 'createdDate', label: 'Created Date' }
    ];
    this.selectedDashboard = 'purchase';
    this.baseDateCtrl.setValue('deliveryDate');
    this.startDate.setValue(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000));
    this.endDate.setValue(new Date());

    // Load dashboard data after fallback configuration is set
    setTimeout(() => {
      this.loadDashboardData();
    }, 100);
  }

  ngOnInit(): void {
    this.initializeFilters();
    this.loadBranches();
    // Don't load dashboard data immediately - wait for config to load
    // Dashboard data will be loaded after config is ready
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeFilters(): void {
    // Location filter
    this.locationFilterCtrl.valueChanges
      .pipe(
        debounceTime(200),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((value: string | null) => {
        this.filterBranches(value || '');
      });
  }

  private loadBranches(): void {
    this.shareDataService.selectedBranchesSource
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.branches = data || [];
        this.filteredBranches = [...this.branches];
        
        if (this.branches.length === 1) {
          this.selectedLocation = this.branches[0].restaurantIdOld;
        }
      });
  }

  private filterBranches(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredBranches = [...this.branches];
    } else {
      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\s/g, '');
      this.filteredBranches = this.branches.filter(branch =>
        branch.branchName.toLowerCase().replace(/\s/g, '').includes(normalizedSearchTerm)
      );
    }
  }

  loadDashboardData(): void {
    if (!this.startDate.value || !this.endDate.value || !this.selectedDashboard) {
      return;
    }

    this.isLoading = true;

    const filters = {
      locations: this.selectedLocation ? [this.selectedLocation] : [],
      startDate: this.formatDate(this.startDate.value),
      endDate: this.formatDate(this.endDate.value),
      baseDate: this.baseDateCtrl.value || 'deliveryDate'
    };

    const request = {
      tenant_id: this.user.tenantId,
      filters: filters,
      user_query: '',
      use_default_charts: true,
      dashboard_type: this.selectedDashboard
    };

    this.smartDashboardService.getSmartDashboardData(request)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.processDashboardData(response.data);
          } else {
            this.clearDashboardData();
          }
          this.isLoading = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.clearDashboardData();
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  private clearDashboardData(): void {
    this.summaryCards = [];
    this.charts = [];
    this.cardGroups = [];
    this.cardGroupsData = null;
    this.useGroupedCards = false;
  }

  private processDashboardData(data: any): void {
    // Process summary cards using config service
    this.summaryCards = data.summary_items?.map((item: any) => ({
      icon: item.icon || this.configService.getSummaryCardIcon(item.data_type),
      value: item.value,
      label: item.label,
      color: this.configService.getSummaryCardColor(item.data_type),
      data_type: item.data_type
    })) || [];

    // Process grouped cards if available
    if (data.card_groups && data.card_groups.grouping_enabled) {
      this.cardGroupsData = data.card_groups;
      this.cardGroups = data.card_groups.groups?.map((group: any) => ({
        ...group,
        cards: group.cards?.map((item: any) => ({
          icon: item.icon || this.configService.getSummaryCardIcon(item.data_type),
          value: item.value,
          label: item.label,
          color: this.configService.getSummaryCardColor(item.data_type),
          data_type: item.data_type
        })) || []
      })) || [];
      this.useGroupedCards = this.cardGroups.length > 0;
    } else {
      this.cardGroups = [];
      this.cardGroupsData = null;
      this.useGroupedCards = false;
    }

    // Process charts using chart renderer service
    this.charts = data.charts?.map((chart: any) =>
      this.chartRenderer.processChart(chart)
    ) || [];
  }

  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  onLocationChange(): void {
    this.loadDashboardData();
  }

  onDateChange(): void {
    this.loadDashboardData();
  }

  onDashboardChange(): void {
    this.loadDashboardData();
  }

  onSearchQuery(): void {
    const query = this.searchQuery.value?.trim();
    if (query) {
      this.loadDashboardDataWithQuery(query);
    } else {
      this.loadDashboardData();
    }
  }

  private loadDashboardDataWithQuery(query: string): void {
    if (!this.startDate.value || !this.endDate.value || !this.selectedDashboard) {
      return;
    }

    this.isLoading = true;

    const filters = {
      locations: this.selectedLocation ? [this.selectedLocation] : [],
      startDate: this.formatDate(this.startDate.value),
      endDate: this.formatDate(this.endDate.value),
      baseDate: this.baseDateCtrl.value || 'deliveryDate'
    };

    const request = {
      tenant_id: this.user.tenantId,
      filters: filters,
      user_query: query,
      use_default_charts: false,
      dashboard_type: this.selectedDashboard
    };

    this.smartDashboardService.getSmartDashboardData(request)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.processDashboardData(response.data);
          } else {
            this.clearDashboardData();
          }
          this.isLoading = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.clearDashboardData();
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  // Dynamic chart methods using services
  getChartData(chart: ChartModel): ChartData {
    return chart.data;
  }

  getChartType(chart: ChartModel): ChartType {
    return this.chartRenderer.getChartType(chart.type);
  }

  getChartOptions(chart: ChartModel): ChartConfiguration['options'] {
    return chart.options || this.configService.getDefaultChartOptions();
  }

  getChartCssClass(chart: ChartModel): string {
    return this.chartRenderer.getChartCssClass(chart);
  }

  // Grouped cards methods
  toggleGroupExpansion(groupId: string): void {
    const group = this.cardGroups.find(g => g.id === groupId);
    if (group) {
      group.is_expanded = !group.is_expanded;
    }
  }

  expandAllGroups(): void {
    this.cardGroups.forEach(group => {
      group.is_expanded = true;
    });
  }

  collapseAllGroups(): void {
    this.cardGroups.forEach(group => {
      group.is_expanded = false;
    });
  }

  toggleGroupedView(): void {
    this.useGroupedCards = !this.useGroupedCards;
  }
}

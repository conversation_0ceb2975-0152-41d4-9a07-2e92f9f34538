{"ast": null, "code": "import { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nclass SmartDashboardService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = environment.engineUrl;\n  }\n  /**\n   * Get dashboard configuration\n   */\n  getDashboardConfig() {\n    return this.http.get(`${this.baseUrl}api/smart-dashboard/config`);\n  }\n  /**\n   * Get smart dashboard data with filters and optional query\n   */\n  getSmartDashboardData(request) {\n    return this.http.post(`${this.baseUrl}api/smart-dashboard/smart_ask`, request);\n  }\n  /**\n   * Get default dashboard data for a tenant\n   */\n  getDefaultDashboardData(tenantId, filters, dashboardType = 'purchase') {\n    const request = {\n      tenant_id: tenantId,\n      filters: filters,\n      user_query: '',\n      use_default_charts: true,\n      dashboard_type: dashboardType\n    };\n    return this.getSmartDashboardData(request);\n  }\n  /**\n   * Query dashboard with natural language\n   */\n  queryDashboard(tenantId, query, filters) {\n    const request = {\n      tenant_id: tenantId,\n      filters: filters,\n      user_query: query,\n      use_default_charts: false\n    };\n    return this.getSmartDashboardData(request);\n  }\n  /**\n   * Format currency value\n   */\n  formatCurrency(value, currency = '₹') {\n    if (value >= 10000000) {\n      // 1 crore\n      return `${currency}${(value / 10000000).toFixed(2)}Cr`;\n    } else if (value >= 100000) {\n      // 1 lakh\n      return `${currency}${(value / 100000).toFixed(2)}L`;\n    } else if (value >= 1000) {\n      // 1 thousand\n      return `${currency}${(value / 1000).toFixed(2)}K`;\n    } else {\n      return `${currency}${value.toFixed(2)}`;\n    }\n  }\n  /**\n   * Format number with Indian numbering system\n   */\n  formatNumber(value) {\n    if (value >= 10000000) {\n      // 1 crore\n      return `${(value / 10000000).toFixed(2)}Cr`;\n    } else if (value >= 100000) {\n      // 1 lakh\n      return `${(value / 100000).toFixed(2)}L`;\n    } else if (value >= 1000) {\n      // 1 thousand\n      return `${(value / 1000).toFixed(2)}K`;\n    } else {\n      return value.toString();\n    }\n  }\n  /**\n   * Get light orange theme colors for charts\n   */\n  getChartColors() {\n    return ['#ffb366', '#ffc999', '#ffab66', '#ffd6b3', '#ff9d4d', '#ffe0cc', '#ffb84d', '#fff2e6', '#ffa64d', '#fff5f0' // White Orange\n    ];\n  }\n  /**\n   * Get light orange gradient combinations\n   */\n  getGradientColors() {\n    return {\n      primary: ['#ffb366', '#fff5f0'],\n      secondary: ['#ffc999', '#ffffff'],\n      tertiary: ['#ffab66', '#ffe0cc'],\n      light: ['#ffd6b3', '#ffffff'],\n      subtle: ['#fff2e6', '#ffffff']\n    };\n  }\n  /**\n   * Create gradient background for canvas charts\n   */\n  createGradient(ctx, chartArea, colorKey = 'primary') {\n    const gradientColors = this.getGradientColors()[colorKey];\n    const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);\n    gradient.addColorStop(0, gradientColors[0] + '20'); // 20% opacity at bottom\n    gradient.addColorStop(1, gradientColors[1] + '80'); // 80% opacity at top\n    return gradient;\n  }\n  /**\n   * Process chart data with clean ng2-charts styling\n   */\n  processChartData(chart) {\n    const colors = this.getChartColors();\n    switch (chart.type) {\n      case 'line':\n        return {\n          ...chart.data,\n          datasets: chart.data.datasets.map((dataset, index) => ({\n            ...dataset,\n            borderColor: colors[index % colors.length],\n            backgroundColor: colors[index % colors.length] + '20',\n            fill: true,\n            tension: 0.3,\n            borderWidth: 2,\n            pointRadius: 3,\n            pointHoverRadius: 5\n          }))\n        };\n      case 'bar':\n        return {\n          ...chart.data,\n          datasets: chart.data.datasets.map(dataset => ({\n            ...dataset,\n            backgroundColor: chart.data.labels.map((_, i) => colors[i % colors.length]),\n            borderColor: chart.data.labels.map((_, i) => colors[i % colors.length]),\n            borderWidth: 1\n          }))\n        };\n      case 'doughnut':\n      case 'pie':\n        return {\n          ...chart.data,\n          datasets: chart.data.datasets.map(dataset => ({\n            ...dataset,\n            backgroundColor: chart.data.labels.map((_, i) => colors[i % colors.length]),\n            borderColor: '#ffffff',\n            borderWidth: 2\n          }))\n        };\n      default:\n        return chart.data;\n    }\n  }\n  /**\n   * Get icon for summary card based on data type\n   */\n  getSummaryCardIcon(dataType, label) {\n    const iconMap = {\n      'currency': 'attach_money',\n      'number': 'numbers',\n      'percentage': 'percent',\n      'vendor': 'store'\n    };\n    // Check label for specific icons\n    if (label.toLowerCase().includes('order')) {\n      return 'shopping_cart';\n    } else if (label.toLowerCase().includes('amount') || label.toLowerCase().includes('value')) {\n      return 'attach_money';\n    } else if (label.toLowerCase().includes('vendor') || label.toLowerCase().includes('supplier')) {\n      return 'store';\n    } else if (label.toLowerCase().includes('average')) {\n      return 'trending_up';\n    }\n    return iconMap[dataType] || 'analytics';\n  }\n  /**\n   * Validate date range\n   */\n  validateDateRange(startDate, endDate) {\n    if (!startDate || !endDate) {\n      return false;\n    }\n    // End date should be after start date\n    if (endDate <= startDate) {\n      return false;\n    }\n    // Date range should not be more than 1 year\n    const oneYear = 365 * 24 * 60 * 60 * 1000;\n    if (endDate.getTime() - startDate.getTime() > oneYear) {\n      return false;\n    }\n    return true;\n  }\n  /**\n   * Get default date range (last 30 days)\n   */\n  getDefaultDateRange() {\n    const endDate = new Date();\n    const startDate = new Date();\n    startDate.setDate(startDate.getDate() - 30);\n    return {\n      startDate,\n      endDate\n    };\n  }\n  static {\n    this.ɵfac = function SmartDashboardService_Factory(t) {\n      return new (t || SmartDashboardService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SmartDashboardService,\n      factory: SmartDashboardService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\nexport { SmartDashboardService };", "map": {"version": 3, "names": ["environment", "SmartDashboardService", "constructor", "http", "baseUrl", "engineUrl", "getDashboardConfig", "get", "getSmartDashboardData", "request", "post", "getDefaultDashboardData", "tenantId", "filters", "dashboardType", "tenant_id", "user_query", "use_default_charts", "dashboard_type", "queryDashboard", "query", "formatCurrency", "value", "currency", "toFixed", "formatNumber", "toString", "getChartColors", "getGradientColors", "primary", "secondary", "tertiary", "light", "subtle", "createGradient", "ctx", "chartArea", "colorKey", "gradientColors", "gradient", "createLinearGradient", "bottom", "top", "addColorStop", "processChartData", "chart", "colors", "type", "data", "datasets", "map", "dataset", "index", "borderColor", "length", "backgroundColor", "fill", "tension", "borderWidth", "pointRadius", "pointHoverRadius", "labels", "_", "i", "getSummaryCardIcon", "dataType", "label", "iconMap", "toLowerCase", "includes", "validateDateRange", "startDate", "endDate", "oneYear", "getTime", "getDefaultDateRange", "Date", "setDate", "getDate", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\digii\\digitorywebv4\\src\\app\\services\\smart-dashboard.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\n\nexport interface DashboardFilters {\n  locations: string[];\n  startDate: string;\n  endDate: string;\n  baseDate: string;\n}\n\nexport interface SmartDashboardRequest {\n  tenant_id: string;\n  filters: DashboardFilters;\n  user_query: string;\n  use_default_charts: boolean;\n  dashboard_type: string;\n}\n\nexport interface SummaryItem {\n  icon: string;\n  value: string;\n  label: string;\n  data_type: string;\n}\n\nexport interface ChartDataset {\n  label: string;\n  data: number[];\n  backgroundColor: string[];\n  borderColor: string[];\n}\n\nexport interface ChartData {\n  labels: string[];\n  datasets: ChartDataset[];\n}\n\nexport interface Chart {\n  id: string;\n  title: string;\n  type: string;\n  data: ChartData;\n}\n\nexport interface DashboardResponse {\n  charts: Chart[];\n  summary_items: SummaryItem[];\n}\n\nexport interface SmartDashboardApiResponse {\n  status: string;\n  data: DashboardResponse;\n}\n\nexport interface DashboardConfig {\n  chart_colors: string[];\n  chart_types: { [key: string]: string };\n  currency: {\n    code: string;\n    symbol: string;\n  };\n  dashboard_types: string[];\n}\n\nexport interface DashboardConfigResponse {\n  status: string;\n  data: DashboardConfig;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SmartDashboardService {\n  private baseUrl: string = environment.engineUrl;\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * Get dashboard configuration\n   */\n  getDashboardConfig(): Observable<DashboardConfigResponse> {\n    return this.http.get<DashboardConfigResponse>(`${this.baseUrl}api/smart-dashboard/config`);\n  }\n\n  /**\n   * Get smart dashboard data with filters and optional query\n   */\n  getSmartDashboardData(request: SmartDashboardRequest): Observable<SmartDashboardApiResponse> {\n    return this.http.post<SmartDashboardApiResponse>(`${this.baseUrl}api/smart-dashboard/smart_ask`, request);\n  }\n\n  /**\n   * Get default dashboard data for a tenant\n   */\n  getDefaultDashboardData(tenantId: string, filters: DashboardFilters, dashboardType: string = 'purchase'): Observable<SmartDashboardApiResponse> {\n    const request: SmartDashboardRequest = {\n      tenant_id: tenantId,\n      filters: filters,\n      user_query: '',\n      use_default_charts: true,\n      dashboard_type: dashboardType\n    };\n\n    return this.getSmartDashboardData(request);\n  }\n\n  /**\n   * Query dashboard with natural language\n   */\n  queryDashboard(tenantId: string, query: string, filters: DashboardFilters): Observable<SmartDashboardApiResponse> {\n    const request: SmartDashboardRequest = {\n      tenant_id: tenantId,\n      filters: filters,\n      user_query: query,\n      use_default_charts: false\n    };\n\n    return this.getSmartDashboardData(request);\n  }\n\n  /**\n   * Format currency value\n   */\n  formatCurrency(value: number, currency: string = '₹'): string {\n    if (value >= 10000000) { // 1 crore\n      return `${currency}${(value / 10000000).toFixed(2)}Cr`;\n    } else if (value >= 100000) { // 1 lakh\n      return `${currency}${(value / 100000).toFixed(2)}L`;\n    } else if (value >= 1000) { // 1 thousand\n      return `${currency}${(value / 1000).toFixed(2)}K`;\n    } else {\n      return `${currency}${value.toFixed(2)}`;\n    }\n  }\n\n  /**\n   * Format number with Indian numbering system\n   */\n  formatNumber(value: number): string {\n    if (value >= 10000000) { // 1 crore\n      return `${(value / 10000000).toFixed(2)}Cr`;\n    } else if (value >= 100000) { // 1 lakh\n      return `${(value / 100000).toFixed(2)}L`;\n    } else if (value >= 1000) { // 1 thousand\n      return `${(value / 1000).toFixed(2)}K`;\n    } else {\n      return value.toString();\n    }\n  }\n\n  /**\n   * Get light orange theme colors for charts\n   */\n  getChartColors(): string[] {\n    return [\n      '#ffb366', // Light Orange Primary\n      '#ffc999', // Very Light Orange\n      '#ffab66', // Light Orange Secondary\n      '#ffd6b3', // Pale Orange\n      '#ff9d4d', // Medium Light Orange\n      '#ffe0cc', // Very Pale Orange\n      '#ffb84d', // Light Amber\n      '#fff2e6', // Almost White Orange\n      '#ffa64d', // Light Orange Tertiary\n      '#fff5f0'  // White Orange\n    ];\n  }\n\n  /**\n   * Get light orange gradient combinations\n   */\n  getGradientColors(): { [key: string]: string[] } {\n    return {\n      primary: ['#ffb366', '#fff5f0'],\n      secondary: ['#ffc999', '#ffffff'],\n      tertiary: ['#ffab66', '#ffe0cc'],\n      light: ['#ffd6b3', '#ffffff'],\n      subtle: ['#fff2e6', '#ffffff']\n    };\n  }\n\n  /**\n   * Create gradient background for canvas charts\n   */\n  createGradient(ctx: CanvasRenderingContext2D, chartArea: any, colorKey: string = 'primary'): CanvasGradient {\n    const gradientColors = this.getGradientColors()[colorKey];\n    const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);\n    gradient.addColorStop(0, gradientColors[0] + '20'); // 20% opacity at bottom\n    gradient.addColorStop(1, gradientColors[1] + '80'); // 80% opacity at top\n    return gradient;\n  }\n\n  /**\n   * Process chart data with clean ng2-charts styling\n   */\n  processChartData(chart: Chart): any {\n    const colors = this.getChartColors();\n\n    switch (chart.type) {\n      case 'line':\n        return {\n          ...chart.data,\n          datasets: chart.data.datasets.map((dataset, index) => ({\n            ...dataset,\n            borderColor: colors[index % colors.length],\n            backgroundColor: colors[index % colors.length] + '20', // 20% opacity\n            fill: true,\n            tension: 0.3,\n            borderWidth: 2,\n            pointRadius: 3,\n            pointHoverRadius: 5\n          }))\n        };\n\n      case 'bar':\n        return {\n          ...chart.data,\n          datasets: chart.data.datasets.map((dataset) => ({\n            ...dataset,\n            backgroundColor: chart.data.labels.map((_, i) => colors[i % colors.length]),\n            borderColor: chart.data.labels.map((_, i) => colors[i % colors.length]),\n            borderWidth: 1\n          }))\n        };\n\n      case 'doughnut':\n      case 'pie':\n        return {\n          ...chart.data,\n          datasets: chart.data.datasets.map(dataset => ({\n            ...dataset,\n            backgroundColor: chart.data.labels.map((_, i) => colors[i % colors.length]),\n            borderColor: '#ffffff',\n            borderWidth: 2\n          }))\n        };\n\n      default:\n        return chart.data;\n    }\n  }\n\n  /**\n   * Get icon for summary card based on data type\n   */\n  getSummaryCardIcon(dataType: string, label: string): string {\n    const iconMap: { [key: string]: string } = {\n      'currency': 'attach_money',\n      'number': 'numbers',\n      'percentage': 'percent',\n      'vendor': 'store'\n    };\n\n    // Check label for specific icons\n    if (label.toLowerCase().includes('order')) {\n      return 'shopping_cart';\n    } else if (label.toLowerCase().includes('amount') || label.toLowerCase().includes('value')) {\n      return 'attach_money';\n    } else if (label.toLowerCase().includes('vendor') || label.toLowerCase().includes('supplier')) {\n      return 'store';\n    } else if (label.toLowerCase().includes('average')) {\n      return 'trending_up';\n    }\n\n    return iconMap[dataType] || 'analytics';\n  }\n\n  /**\n   * Validate date range\n   */\n  validateDateRange(startDate: Date, endDate: Date): boolean {\n    if (!startDate || !endDate) {\n      return false;\n    }\n\n    // End date should be after start date\n    if (endDate <= startDate) {\n      return false;\n    }\n\n    // Date range should not be more than 1 year\n    const oneYear = 365 * 24 * 60 * 60 * 1000;\n    if (endDate.getTime() - startDate.getTime() > oneYear) {\n      return false;\n    }\n\n    return true;\n  }\n\n  /**\n   * Get default date range (last 30 days)\n   */\n  getDefaultDateRange(): { startDate: Date; endDate: Date } {\n    const endDate = new Date();\n    const startDate = new Date();\n    startDate.setDate(startDate.getDate() - 30);\n\n    return { startDate, endDate };\n  }\n}\n"], "mappings": "AAIA,SAASA,WAAW,QAAQ,gCAAgC;;;AAoE5D,MAGaC,qBAAqB;EAGhCC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,OAAO,GAAWJ,WAAW,CAACK,SAAS;EAER;EAEvC;;;EAGAC,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAA0B,GAAG,IAAI,CAACH,OAAO,4BAA4B,CAAC;EAC5F;EAEA;;;EAGAI,qBAAqBA,CAACC,OAA8B;IAClD,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CAA4B,GAAG,IAAI,CAACN,OAAO,+BAA+B,EAAEK,OAAO,CAAC;EAC3G;EAEA;;;EAGAE,uBAAuBA,CAACC,QAAgB,EAAEC,OAAyB,EAAEC,aAAA,GAAwB,UAAU;IACrG,MAAML,OAAO,GAA0B;MACrCM,SAAS,EAAEH,QAAQ;MACnBC,OAAO,EAAEA,OAAO;MAChBG,UAAU,EAAE,EAAE;MACdC,kBAAkB,EAAE,IAAI;MACxBC,cAAc,EAAEJ;KACjB;IAED,OAAO,IAAI,CAACN,qBAAqB,CAACC,OAAO,CAAC;EAC5C;EAEA;;;EAGAU,cAAcA,CAACP,QAAgB,EAAEQ,KAAa,EAAEP,OAAyB;IACvE,MAAMJ,OAAO,GAA0B;MACrCM,SAAS,EAAEH,QAAQ;MACnBC,OAAO,EAAEA,OAAO;MAChBG,UAAU,EAAEI,KAAK;MACjBH,kBAAkB,EAAE;KACrB;IAED,OAAO,IAAI,CAACT,qBAAqB,CAACC,OAAO,CAAC;EAC5C;EAEA;;;EAGAY,cAAcA,CAACC,KAAa,EAAEC,QAAA,GAAmB,GAAG;IAClD,IAAID,KAAK,IAAI,QAAQ,EAAE;MAAE;MACvB,OAAO,GAAGC,QAAQ,GAAG,CAACD,KAAK,GAAG,QAAQ,EAAEE,OAAO,CAAC,CAAC,CAAC,IAAI;KACvD,MAAM,IAAIF,KAAK,IAAI,MAAM,EAAE;MAAE;MAC5B,OAAO,GAAGC,QAAQ,GAAG,CAACD,KAAK,GAAG,MAAM,EAAEE,OAAO,CAAC,CAAC,CAAC,GAAG;KACpD,MAAM,IAAIF,KAAK,IAAI,IAAI,EAAE;MAAE;MAC1B,OAAO,GAAGC,QAAQ,GAAG,CAACD,KAAK,GAAG,IAAI,EAAEE,OAAO,CAAC,CAAC,CAAC,GAAG;KAClD,MAAM;MACL,OAAO,GAAGD,QAAQ,GAAGD,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE;;EAE3C;EAEA;;;EAGAC,YAAYA,CAACH,KAAa;IACxB,IAAIA,KAAK,IAAI,QAAQ,EAAE;MAAE;MACvB,OAAO,GAAG,CAACA,KAAK,GAAG,QAAQ,EAAEE,OAAO,CAAC,CAAC,CAAC,IAAI;KAC5C,MAAM,IAAIF,KAAK,IAAI,MAAM,EAAE;MAAE;MAC5B,OAAO,GAAG,CAACA,KAAK,GAAG,MAAM,EAAEE,OAAO,CAAC,CAAC,CAAC,GAAG;KACzC,MAAM,IAAIF,KAAK,IAAI,IAAI,EAAE;MAAE;MAC1B,OAAO,GAAG,CAACA,KAAK,GAAG,IAAI,EAAEE,OAAO,CAAC,CAAC,CAAC,GAAG;KACvC,MAAM;MACL,OAAOF,KAAK,CAACI,QAAQ,EAAE;;EAE3B;EAEA;;;EAGAC,cAAcA,CAAA;IACZ,OAAO,CACL,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CAAE;IAAA,CACZ;EACH;EAEA;;;EAGAC,iBAAiBA,CAAA;IACf,OAAO;MACLC,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;MAC/BC,SAAS,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;MACjCC,QAAQ,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;MAChCC,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;MAC7BC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS;KAC9B;EACH;EAEA;;;EAGAC,cAAcA,CAACC,GAA6B,EAAEC,SAAc,EAAEC,QAAA,GAAmB,SAAS;IACxF,MAAMC,cAAc,GAAG,IAAI,CAACV,iBAAiB,EAAE,CAACS,QAAQ,CAAC;IACzD,MAAME,QAAQ,GAAGJ,GAAG,CAACK,oBAAoB,CAAC,CAAC,EAAEJ,SAAS,CAACK,MAAM,EAAE,CAAC,EAAEL,SAAS,CAACM,GAAG,CAAC;IAChFH,QAAQ,CAACI,YAAY,CAAC,CAAC,EAAEL,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IACpDC,QAAQ,CAACI,YAAY,CAAC,CAAC,EAAEL,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IACpD,OAAOC,QAAQ;EACjB;EAEA;;;EAGAK,gBAAgBA,CAACC,KAAY;IAC3B,MAAMC,MAAM,GAAG,IAAI,CAACnB,cAAc,EAAE;IAEpC,QAAQkB,KAAK,CAACE,IAAI;MAChB,KAAK,MAAM;QACT,OAAO;UACL,GAAGF,KAAK,CAACG,IAAI;UACbC,QAAQ,EAAEJ,KAAK,CAACG,IAAI,CAACC,QAAQ,CAACC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,MAAM;YACrD,GAAGD,OAAO;YACVE,WAAW,EAAEP,MAAM,CAACM,KAAK,GAAGN,MAAM,CAACQ,MAAM,CAAC;YAC1CC,eAAe,EAAET,MAAM,CAACM,KAAK,GAAGN,MAAM,CAACQ,MAAM,CAAC,GAAG,IAAI;YACrDE,IAAI,EAAE,IAAI;YACVC,OAAO,EAAE,GAAG;YACZC,WAAW,EAAE,CAAC;YACdC,WAAW,EAAE,CAAC;YACdC,gBAAgB,EAAE;WACnB,CAAC;SACH;MAEH,KAAK,KAAK;QACR,OAAO;UACL,GAAGf,KAAK,CAACG,IAAI;UACbC,QAAQ,EAAEJ,KAAK,CAACG,IAAI,CAACC,QAAQ,CAACC,GAAG,CAAEC,OAAO,KAAM;YAC9C,GAAGA,OAAO;YACVI,eAAe,EAAEV,KAAK,CAACG,IAAI,CAACa,MAAM,CAACX,GAAG,CAAC,CAACY,CAAC,EAAEC,CAAC,KAAKjB,MAAM,CAACiB,CAAC,GAAGjB,MAAM,CAACQ,MAAM,CAAC,CAAC;YAC3ED,WAAW,EAAER,KAAK,CAACG,IAAI,CAACa,MAAM,CAACX,GAAG,CAAC,CAACY,CAAC,EAAEC,CAAC,KAAKjB,MAAM,CAACiB,CAAC,GAAGjB,MAAM,CAACQ,MAAM,CAAC,CAAC;YACvEI,WAAW,EAAE;WACd,CAAC;SACH;MAEH,KAAK,UAAU;MACf,KAAK,KAAK;QACR,OAAO;UACL,GAAGb,KAAK,CAACG,IAAI;UACbC,QAAQ,EAAEJ,KAAK,CAACG,IAAI,CAACC,QAAQ,CAACC,GAAG,CAACC,OAAO,KAAK;YAC5C,GAAGA,OAAO;YACVI,eAAe,EAAEV,KAAK,CAACG,IAAI,CAACa,MAAM,CAACX,GAAG,CAAC,CAACY,CAAC,EAAEC,CAAC,KAAKjB,MAAM,CAACiB,CAAC,GAAGjB,MAAM,CAACQ,MAAM,CAAC,CAAC;YAC3ED,WAAW,EAAE,SAAS;YACtBK,WAAW,EAAE;WACd,CAAC;SACH;MAEH;QACE,OAAOb,KAAK,CAACG,IAAI;;EAEvB;EAEA;;;EAGAgB,kBAAkBA,CAACC,QAAgB,EAAEC,KAAa;IAChD,MAAMC,OAAO,GAA8B;MACzC,UAAU,EAAE,cAAc;MAC1B,QAAQ,EAAE,SAAS;MACnB,YAAY,EAAE,SAAS;MACvB,QAAQ,EAAE;KACX;IAED;IACA,IAAID,KAAK,CAACE,WAAW,EAAE,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MACzC,OAAO,eAAe;KACvB,MAAM,IAAIH,KAAK,CAACE,WAAW,EAAE,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAIH,KAAK,CAACE,WAAW,EAAE,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC1F,OAAO,cAAc;KACtB,MAAM,IAAIH,KAAK,CAACE,WAAW,EAAE,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAIH,KAAK,CAACE,WAAW,EAAE,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC7F,OAAO,OAAO;KACf,MAAM,IAAIH,KAAK,CAACE,WAAW,EAAE,CAACC,QAAQ,CAAC,SAAS,CAAC,EAAE;MAClD,OAAO,aAAa;;IAGtB,OAAOF,OAAO,CAACF,QAAQ,CAAC,IAAI,WAAW;EACzC;EAEA;;;EAGAK,iBAAiBA,CAACC,SAAe,EAAEC,OAAa;IAC9C,IAAI,CAACD,SAAS,IAAI,CAACC,OAAO,EAAE;MAC1B,OAAO,KAAK;;IAGd;IACA,IAAIA,OAAO,IAAID,SAAS,EAAE;MACxB,OAAO,KAAK;;IAGd;IACA,MAAME,OAAO,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;IACzC,IAAID,OAAO,CAACE,OAAO,EAAE,GAAGH,SAAS,CAACG,OAAO,EAAE,GAAGD,OAAO,EAAE;MACrD,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAEA;;;EAGAE,mBAAmBA,CAAA;IACjB,MAAMH,OAAO,GAAG,IAAII,IAAI,EAAE;IAC1B,MAAML,SAAS,GAAG,IAAIK,IAAI,EAAE;IAC5BL,SAAS,CAACM,OAAO,CAACN,SAAS,CAACO,OAAO,EAAE,GAAG,EAAE,CAAC;IAE3C,OAAO;MAAEP,SAAS;MAAEC;IAAO,CAAE;EAC/B;;;uBAlOWvE,qBAAqB,EAAA8E,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAArBjF,qBAAqB;MAAAkF,OAAA,EAArBlF,qBAAqB,CAAAmF,IAAA;MAAAC,UAAA,EAFpB;IAAM;EAAA;;SAEPpF,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}